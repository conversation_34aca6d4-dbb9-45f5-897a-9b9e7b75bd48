<!doctype html>
<html build-time="%BUILD_TIME%">
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
    <link
      rel="stylesheet"
      href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree_CSS&wgId=67"
    />
    <script src="./static/com/config.js"></script>
    <script
      type="text/javascript"
      src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=IndoorThree&wgId=67"
    ></script>
    <script>
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />',
      )
    </script>
    <title>unibest</title>
  </head>

  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
