import _ from '@/utils/lodash'
import hy from './hy'
import jzy from './jzy'
import { stringify } from 'querystringify'

const server = {
  nil: '', // 不拼接服务地址
  upms: '/upms', // 权限
  intelligent: '/intelligent-inspection-service', // 巡检
  hazard: '/ehs-clnt-hazard-service', // 隐患
}

export const api = {
  type: server,
  name: _.merge(hy, jzy),

  getUrl(serviceType: string, apiName: string, query?: any): string {
    const paramsStr = query ? `?${stringify(query)}` : ''
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName
    const _serviceType = serviceType || ''
    return `${_serviceType}${_apiName}${paramsStr}`
  },
}
