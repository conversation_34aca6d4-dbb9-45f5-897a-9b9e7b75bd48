export default {
  inspectTask: {
    pageViewTask: '/video/task/pageViewTask',
    getVideoDeviceInfo: '/video/device/getVideoDeviceInfo',
    queryVideoTaskDisposeByVideoDeviceId: '/video/device/queryVideoTaskDisposeByVideoDeviceId',
    queryNewVideoTaskDisposeByVideoDeviceId: '/video/device/queryEventListByVideoDeviceId',
    taskTopDetail: '/video/task/taskTopDetail',
    taskInspectionDetailStatistic: '/video/task/taskInspectionDetailStatistic',
    taskInspectionPage: '/video/task/taskInspectionPage',
    taskInspectionDetail: '/video/task/taskInspectionDetail',
    taskPointDetailByPositionNo: '/video/task/taskPointDetailByPositionNo',
    qeuryEventDetail: '/hazardRecord/qeuryEventDetail',
    getViewTaskDevice: '/video/task/pageViewTaskDeviceDispose',
  },
  hazard: {
    qeuryEventDetail: '/hazardRecord/qeuryEventDetail',
    getUrgeRecord: '/dispose/getUrgeRecord',
    getRecord: '/dispose/record',
    getQueryDictDataList: '/ehscommom/queryDictDataList',
  },
}
