import { http } from '@/utils/http'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usefileConfigStore = defineStore(
  'fileConfig',
  () => {
    const fileUrl = ref<any>({})
    const setfileConfig = (val: any) => {
      fileUrl.value = val
    }

    async function getfileConfig() {
      return http<any>({
        serviceType: 'hidden',
        method: 'GET',
        url: '/file/getFileConfig',
      })
    }
    const clearfileConfigData = () => {
      fileUrl.value = null
    }

    return {
      fileUrl,
      getfileConfig,
      setfileConfig,
      clearfileConfigData,
    }
  },
  {
    persist: true,
  },
)
