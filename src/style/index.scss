// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #37c2bc;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
  background-color: #f9f9f9 !important;
}

// .zp-custom-refresher-view {
//   margin-top: 0px !important;
// }
.zp-custom-refresher-view {
  // margin-top: 0px !important;
  background-color: #f9f9f9 !important;
}
