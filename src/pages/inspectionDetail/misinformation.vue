<template>
  <view>
    <SafetyNavbar class="navbar" title="隐患整改"></SafetyNavbar>
    <view class="mt-20px">
      <wd-card class="h-[calc(100vh-220px)]" title="整改描述">
        <wd-textarea
          placeholder="请输入具体相关信息"
          :v-model="reviewChecktModel?.dealDesc"
          :maxlength="100"
          show-word-limit
          auto-height
        />
        <view class="text-#222222 text-16px mt-10px">现场照片</view>
        <UpLoadFile :imglen="3" @getFilelist="getFilelist" @getFileObjList="getFileObjList" />
        <view class="text-#222222 text-16px mt-10px flex justify-between"></view>
      </wd-card>
    </view>
    <view class="flex ml-20px mr-20px">
      <wd-button class="flex w-full bottom-btn" size="large" @click="submit">提交</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import UpLoadFile from './compoments/upload-button.vue'
import SafetyNavbar from '@/components/safety-navbar.vue'
import { updateHazardMendAPI } from './compoments/featch'
import { useUserStore } from '@/store'
import { currRoute } from '@/utils'
const currentChecked = ref(0)
const disposeDesc = ref()
const { userInfo } = useUserStore() as any
const { list } = currRoute().query
console.log('list', currRoute().query)
const queryList = computed(() => {
  try {
    // 先解码URL编码的字符串，然后解析JSON
    const decodedList = decodeURIComponent(list as string)
    return JSON.parse(decodedList)
  } catch (error) {
    console.error('JSON解析失败:', error)
    console.error('原始数据:', list)
    // 返回一个默认对象，避免页面崩溃
    return {
      disposeId: '',
      id: '',
    }
  }
})
console.log('queryList', queryList.value)
const reviewChecktModel = ref({
  dealDesc: '',
  dealIsSolved: 0,
  dealUserId: userInfo.id,
  dealUserName: userInfo.userName,
  dealUserPhone: userInfo.userTelphone,
  disposeId: queryList.value.disposeId,
  filePathList: [],
  randomCheckEventId: queryList.value.id,
  reviewState: 0,
  zhId: userInfo.zhId || '',
})

const getFilelist = (fileList: any) => {
  console.log('fileList -----> 🚀', fileList)
}

const getFileObjList = (fileObjList: any) => {
  console.log('fileObjList -----> 🚀', fileObjList)
  reviewChecktModel.value.filePathList = fileObjList
}

const handleClick = (val: number) => {
  currentChecked.value = val
  reviewChecktModel.value.dealIsSolved = val
}
async function submit() {
  if (reviewChecktModel.value.dealDesc === '') {
    return uni.showToast({
      icon: 'none',
      title: '请填写描述信息',
    })
  }
  if (+reviewChecktModel.value.filePathList.length === 0) {
    return uni.showToast({
      icon: 'none',
      title: '请上传现场照片',
    })
  }

  console.log('reviewChecktModel.value -----> 🚀', reviewChecktModel.value)
  uni.showLoading({
    title: '上传中...',
    mask: true,
  })

  try {
    const res = await updateHazardMendAPI(reviewChecktModel.value)
    if (res.code === 'success') {
      uni.showToast({
        title: '操作成功',
      })
      uni.navigateTo({
        url: '/pages/event-handling/alarmDetails/hiddenDetails/index',
      })
    }
  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      icon: 'none',
      title: '提交失败，请重试',
    })
  } finally {
    uni.hideLoading()
  }
}

function customUpload(file, formData, options) {
  options.onSuccess({}, file, formData)
}

onUnload(() => {
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
:deep(.wd-cell__wrapper) {
  display: block;
  margin-left: -10px;
}
:deep(.wd-textarea__value.is-show-limit) {
  padding-bottom: 0;
}

:deep(.wd-textarea, .wd-textarea__value.is-show-limit) {
  padding: 0;
}
hr {
  background: #ccc;
}

.bottom-btn {
  margin: auto;
}
</style>
