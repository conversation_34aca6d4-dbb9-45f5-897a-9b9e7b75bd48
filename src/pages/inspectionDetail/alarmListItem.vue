<template>
  <SafetyNavbar class="navbar" :title="EVENT_LABEL[list?.eventType]"></SafetyNavbar>
  <template v-if="list?.eventType">
    <div class="h-full">
      <FloorGis
        class="h-[calc(100%-400px)]"
        v-if="floorId"
        :type="gisType"
        :build-id="buildId"
        :floor-id="floorId"
        :device-list="deviceList"
      />
    </div>
    <view class="bottom-card white-bg px-10px py-18px mt-10px fixed bottom-0 left-0 right-0">
      <view class="flex item-center justify-between">
        <view class="flex items-center">
          <view class="text-16px font-bold mb-15px mr-10px">{{ list?.deviceTypeName }}</view>
          <view
            class="text-12px text-white h-20px px-10px event-type-flag"
            :class="eventClassMap[list.eventType]"
          >
            {{ EVENT_LABEL[list.eventType] }}
          </view>
        </view>

        <view class="mr-[10px] text-[#00f]" @click="setIsShowImg" v-if="list.fireImage">
          {{ isShowImg ? '收起照片' : '展开照片' }}
        </view>
      </view>
      <view class="mb-10px text-14px" v-for="item in fieldList" :key="item.label">
        <span class="text-#222222">{{ item.label }}</span>
        ：
        <span class="text-#222222">{{ item.value }}</span>
      </view>
      <view class="text-#222222" v-if="!isBtnProps">
        平台最新接收时间：{{ list.lastEventTime }}
      </view>
      <view
        class="mt-10px bottom-button-wrap"
        v-if="!isBtnProps && currRoute().query.eventType === '1'"
      >
        <view class="item">
          <div size="large" @click="truePolice" class="text">真警</div>
        </view>
        <view class="item2">
          <div @click="misinformation" class="text">误报</div>
        </view>
      </view>
    </view>
  </template>
</template>

<script setup lang="ts">
import FloorGis from '@/gis-floor/floorGis.vue'
import { EVENT_LABEL, BUSINESS } from '@/utils/bussiness'
import SafetyNavbar from '@/components/safety-navbar.vue'
import { currRoute } from '@/utils'
import { getFieldList } from './config'
import { useUserStore } from '@/store'
import { getWarnExtDetailAPI } from './fetch'
import { EGisType } from '@/gis-floor/constant'
import { getDevicelnfoByld } from '../deviceLocation/fetchData'
import { useRoute } from 'vue-router'

const route = useRoute()

const store = useUserStore()
const { userInfo } = store as any
const eventClassMap = {
  1: 'bg-color-fire',
  2: 'bg-color-warning',
  3: 'bg-color-fault',
  4: 'bg-color-hidden',
  5: 'bg-color-action',
  7: 'bg-color-offline',
}

const list = ref()
const isBtnProps = computed(() => !!list.value.disposeState)
const fieldList = computed(() => removePlusSigns(getFieldList(list.value)))
const isShowImg = ref(true)
const setIsShowImg = () => {
  isShowImg.value = !isShowImg.value
}
const gisType = EGisType.DEVICELOC
const buildId = ref('')
const floorId = ref('')
const deviceList = ref<any[]>([])
function getItemInfo() {
  getItemInfoHidden()
}

async function getItemInfoHidden() {
  const res: any = await getWarnExtDetailAPI({ id: currRoute().query.id })
  if (res.code === 'success') {
    const querys = {
      ...res.data,
      eventType: '4',
      priorityEventType: '4',
      ...res.data.deviceInfo,
    }
    list.value = querys
  }
}
const getData = () => {
  getDevicelnfoByld(route.query.deviceId || '').then((res) => {
    if (res.data.length > 0) {
      const data = res.data[0]
      deviceList.value = [
        {
          deviceId: data.deviceId,
          deviceNum: data.deviceNum,
          deviceTypeId: data.deviceTypeId,
          deviceAddress: data.deviceAddress,
          buildingId: data.buildingId,
          floorId: data.floorId,
          mapX: data.mapX,
          mapY: data.mapY,
          mapZ: data.mapZ,
          latitude: data.latitude,
          longitude: data.longitude,
        },
      ]

      buildId.value = deviceList.value[0]?.buildingId
      floorId.value = deviceList.value[0]?.floorId
    }
  })
}
getData()
function removePlusSigns(data) {
  return data?.map((item) => ({
    ...item,
    value: item.value.replace(/\+/g, ''),
  }))
}
function misinformation() {
  uni.navigateTo({
    url: `/pages/event-handling/alarmDetails/fireDetails/misinformation?list=${JSON.stringify(list.value)}`,
  })
}

function truePolice() {
  list.value._loop = BUSINESS.normalizeLoop(list.value)
  list.value._deviceAddress = BUSINESS.normalizeAddress(list.value)
  uni.navigateTo({
    url: `/pages/event-handling/alarmDetails/fireDetails/truePolice?list=${JSON.stringify(list.value)}`,
  })
}

getItemInfo()
</script>

<style lang="scss" scoped>
.bg-color-fire {
  background-color: var(--event-color-fire);
}
.bg-color-warning {
  background-color: var(--event-color-warning);
}
.bg-color-fault {
  background-color: var(--event-color-fault);
}
.bg-color-hidden {
  background-color: var(--event-color-hidden);
}
.bg-color-action {
  background-color: var(--event-color-action);
}
.bg-color-offline {
  background-color: var(--event-color-offline);
}
.event-type-flag {
  border-radius: 10px 10px 2px 10px;
}
.bottom-card {
  height: auto;
  background-color: #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}
.bottom-button-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: #fff;
  .item {
    position: relative;
    box-sizing: border-box;
    width: calc(50% - 50px);
    min-width: 100px;
    height: 44px;
    margin: 0 8px;
    border: 1px solid #ee0707;
    transform: skewX(20deg);
  }
  .item2 {
    position: relative;
    box-sizing: border-box;
    width: calc(50% - 50px);
    min-width: 100px;
    height: 44px;
    margin: 0 8px;
    border: 1px solid #4d80f0;
    transform: skewX(20deg);
  }

  .item:first-child {
    background: red;
    border-left-width: 0;
  }
  .item2:last-child {
    background: #4d80f0;
    border-right-width: 0;
  }
  .item .text,
  .item2 .text {
    position: relative;
    top: -1px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 120%;
    height: 44px;
    transform: skewX(-20deg);
  }

  .item:first-child .text {
    left: -30%;
    padding: 0 10% 0 20%;
    background: red;
    border: 1px solid #ee6f07;
    border-right-width: 0;
    border-radius: 30px 0 0 30px;
  }
  .item2:last-child .text {
    left: 10%;
    padding: 0 20% 0 10%;
    background: #4d80f0;
    border: 1px solid #4d80f0;
    border-left-width: 0;
    border-radius: 0 30px 30px 0;
  }
}

:deep(.box-border) {
  padding-top: 0 !important;
}
</style>
