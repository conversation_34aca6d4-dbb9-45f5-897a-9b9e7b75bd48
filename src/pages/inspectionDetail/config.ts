import { BUSINESS } from '@/utils/bussiness'

export function getFieldList(deviceInfo) {
  const loop = BUSINESS.normalizeLoop(deviceInfo)
  const deviceAddress = BUSINESS.normalizeAddress(deviceInfo)
  const deviceNum = deviceInfo.deviceNum || '未知'
  const deviceClass = deviceInfo.deviceClassification
  if (+deviceClass === 1) {
    return [
      {
        label: '主机回路点位',
        value: loop,
      },
      {
        label: '设备位置',
        value: deviceAddress,
      },
    ]
  } else if (+deviceClass === 3 || +deviceClass === 6) {
    return [
      {
        label: 'IMEI',
        value: deviceNum,
      },
      {
        label: '设备位置',
        value: deviceAddress,
      },
    ]
  } else {
    return [
      {
        label: '设备编号',
        value: deviceNum,
      },
      {
        label: '设备位置',
        value: deviceAddress,
      },
    ]
  }
}
