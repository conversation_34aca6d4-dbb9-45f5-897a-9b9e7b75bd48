<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '上传按钮',
  },
}
</route>
<template>
  <view style="display: flex; flex-wrap: wrap">
    <view class="fileList" v-for="(item, index) in data" :key="index">
      <wd-img
        style="width: 100%; height: 90%"
        :src="imgUrl + item.fileUrl"
        :enable-preview="true"
      />
      <wd-button @click="deleteImg(data, item)" type="text">删除</wd-button>
    </view>
    <view @click="chooseFileUpload" class="upload_btn">
      <wd-icon name="fill-camera" size="1.5rem" style="color: #b8b8b8"></wd-icon>
      <p>添加图片</p>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { postFileDeleteAPI, uploadFileAPI } from './featch'
import { PermissionRes } from '@/hooks/useCamera'
import { useAppStore, usefileConfigStore } from '@/store'
import { autoCompressImage } from '@/utils/imageCompressor'

const props = defineProps<{
  imglen?: number
  imginfo?: []
}>()

const imgUrl = ref()
const usefileStore = usefileConfigStore()

const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  imgUrl.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  imgUrl.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// 移除直接使用环境变量，改用接口定义
// 返回对象数组
const data = ref<any[]>([])
// const imgsrc = ''
// const text = ref('上传文件')
const load = ref(false)
const { checkCamera } = useCamera()
function chooseFileUpload() {
  const imgslen = props.imglen ? props.imglen : 10
  if (data.value.length >= imgslen) {
    uni.showToast({
      icon: 'none',
      title: '最多上传' + imgslen + '张',
    })
  }
  if (currentEnvInfo.osAndroid ?? false) {
    checkCamera()
      .then((res) => {
        if (res === PermissionRes.PASS) {
          useUpload()
        } else if (res === PermissionRes.REJECTED) {
          uni
            .showToast({
              title: '请前往设置中打开权限',
            })
            .then(() => {
              // uni.navigateBack()
            })
        } else if (res === PermissionRes.TOOPEN) {
          uni
            .showToast({
              title: '请重新进入应用，并打开相机权限',
            })
            .then(() => {
              // uni.navigateBack()
            })
        } else {
          uni
            .showToast({
              title: '未知权限结果，请联系管理员',
            })
            .then(() => {
              // uni.navigateBack()
            })
        }
      })
      .catch((e) => {
        useUpload()
        // uni
        //   .showToast({
        //     title: String(e),
        //   })
        //   .then(() => {
        //     uni.navigateBack()
        //   })
      })
  } else {
    console.log('current is h5 web')
    useUpload()
  }
  // useUpload()
}

const $emit = defineEmits(['getFilelist', 'getFileObjList', 'deleteImg'])
// 返回数组
const filelist = ref<string[]>([])

const fileobjlist = ref<string[]>([])
function useUpload() {
  const formData = new FormData()
  console.log('调用相机')
  uni.chooseImage({
    count: 10,
    sourceType: ['album', 'camera'],
    success(res: any) {
      console.log(res)
      const fileSizeMB = res.tempFiles[0].size / (1024 * 1024)
      if (fileSizeMB > 10) {
        uni.showToast({
          title: '文件过大，请选择小于10MB的图片',
          icon: 'none',
        })
        // $emit('getFilelist', [])
        // $emit('getFileObjList', [])
        return
      }

      uni.showLoading({
        title: '上传中...',
        icon: 'none',
      })
      for (let i = 0; i < res.tempFiles.length; i++) {
        autoCompressImage(res.tempFiles[i]).then((compressedFile) => {
          formData.append('files', compressedFile)
          uploadFile(formData, res.tempFilePaths[i])
        })
      }
    },
    fail: function (chooseImageError) {
      console.log(chooseImageError)
      uni.showToast({
        title: chooseImageError,
        icon: 'none',
        duration: 2000,
      })
    },
  })
}

// 上传图片
async function uploadFile(formData, tempFilePath) {
  try {
    const upfiles: any = await uploadFileAPI(formData, tempFilePath)
    console.log('upfiles -----> 🚀', upfiles)
    if (upfiles.data && upfiles.data.length > 0) {
      data.value.push(upfiles.data[0])
      filelist.value.push(upfiles.data[0].fileUrl)
      $emit('getFilelist', filelist.value)
      $emit('getFileObjList', data.value)
    }
    uni.hideLoading()
  } catch (error) {
    console.error('上传失败:', error)
    uni.hideLoading()
    uni.showToast({
      icon: 'none',
      title: '上传失败，请重试',
    })
  }
}

function deleteImg(datainfo, item) {
  // console.log(JSON.stringify(data) + '=========组件图片列表===删除方法============')
  // console.log(JSON.stringify(item) + '=========组件删除方法============')
  // 图片回显列表
  const datalist = JSON.parse(JSON.stringify(datainfo))
  // 点击要删除的图片
  const delitem = JSON.parse(JSON.stringify(item))

  // 删除
  console.log(
    datalist.filter((info) => info.fileUrl !== delitem.fileUrl),
    '======',
  )
  data.value = datalist.filter((info) => info.fileUrl !== delitem.fileUrl)
  filelist.value = data.value.map((info) => info.fileUrl)
  console.log(data.value)
  postFileDeleteAPI(delitem.fileUrl)
  $emit('getFileObjList', JSON.parse(JSON.stringify(data.value)))
  $emit('getFilelist', JSON.stringify(filelist.value))
  // $emit('deleteImg', value)
}

// this.$emit('deleteImg', value)
// const deleteImg = (event) => {
//   console.log(event, '=========删除图片====================')
// }

// defineExpose({ deleteImg })
</script>

<style lang="scss" scoped>
.upload_btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 4.125rem;
  height: 4.125rem;
  padding: 0 0.2rem 0.7rem 0.2rem;
  margin: 8px;
  // text-align: center;
  background-color: rgba(238, 238, 238, 0.91);
  border-radius: 0.375rem;
  p {
    font-size: 0.75rem;
    color: #b8b8b8;
  }
}
.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
