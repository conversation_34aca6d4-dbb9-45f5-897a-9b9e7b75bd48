import { http } from '@/utils/http'
import { api } from '@/api'

// 文件删除接口
export const postFileDeleteAPI = (fileurl: string) => {
  return http<any>({
    method: 'POST',
    url: api.getUrl(api.type.hazard, '/file/delete?url=' + fileurl),
  })
}

// 文件上传接口
export const uploadFileAPI = (formData: any, tempFilePath: string) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: api.getUrl(api.type.hazard, '/file/uploads'),
      filePath: tempFilePath,
      name: 'files',
      formData,
      success: (uploadFileRes) => {
        if (uploadFileRes.data) {
          const upfiles = JSON.parse(uploadFileRes.data)
          resolve(upfiles)
        } else {
          reject(new Error('上传失败'))
        }
      },
      fail: (error) => {
        reject(error)
      },
    })
  })
}

// 隐患整改提交接口
export const updateHazardMendAPI = (
  data: any,
): Promise<{ code: string; msg: string; data: any }> => {
  return http<any>({
    method: 'POST',
    url: api.getUrl(api.type.hazard, '/hazardRandomCheck/updateHazardMend'),
    data,
  }) as any
}
