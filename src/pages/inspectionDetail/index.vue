<template>
  <div class="flex flex-col h-full relative">
    <SafetyNavbar class="navbar" title="隐患详情"></SafetyNavbar>
    <view v-if="info" class="white-bg p-20px scroll-content">
      <view class="content-wrapTop">
        <view class="text-16px text-black font-500">{{ info?.hazardDesc || '其他隐患' }}</view>
        <view class="text-14px mt-10px">隐患位置：{{ info?.hazardPosition || '-' }}</view>
        <view class="flex">
          <wd-button type="text" class="no-bg-btn" size="large" @click="viewLocation" round>
            查看位置
          </wd-button>
        </view>
      </view>

      <wd-collapse title="隐患信息" v-model="collapseValue" class="mb-10px">
        <wd-collapse-item class="content-wrap" title="隐患信息" name="item0">
          <view class="list-info -ml-10px">
            <view class="bottom-1px mb-5px">
              隐患来源：{{ gethdSource(warnInfoList?.hazardSource) || '--' }}
            </view>
            <view class="bottom-1px mb-5px">
              隐患类别：{{ warnInfoList?.hazardTypeName || '--' }}
            </view>
            <view class="bottom-1px mb-5px">隐患描述：{{ warnInfoList?.hazardDesc || '--' }}</view>
          </view>
        </wd-collapse-item>
        <wd-collapse-item class="content-wrap" title="设备信息" name="item1">
          <view class="list-info -ml-10px">
            <view class="bottom-1px mb-5px">
              单位名称：{{ warnInfoList?.deviceInfo?.unitName || '--' }}
            </view>
            <view class="bottom-1px mb-5px">
              设备编号：{{ warnInfoList?.deviceInfo?.deviceId || '--' }}
            </view>
            <view class="bottom-1px mb-5px">
              系统类型：{{ warnInfoList?.deviceInfo?.deviceTypePname || '--' }}
            </view>

            <view class="bottom-1px mb-5px">
              品牌型号：{{
                [
                  warnInfoList?.deviceInfo?.produceInfo?.brand || '',
                  warnInfoList?.deviceInfo?.produceInfo?.model || '',
                ]
                  .filter(Boolean)
                  .join(' ') || '--'
              }}
            </view>
            <view class="bottom-1px mb-5px">
              设备类型：{{ warnInfoList?.deviceInfo?.deviceTypeName || '--' }}
            </view>
            <view class="bottom-1px mb-5px">
              设备位置：{{
                (warnInfoList?.deviceInfo?.buildingName || '') +
                  (warnInfoList?.deviceInfo?.floorName || '') +
                  (warnInfoList?.deviceInfo?.deviceAddress || '') || '--'
              }}
            </view>
            <view class="bottom-1px mb-5px">
              安装日期：{{ warnInfoList?.deviceInfo?.installInfo?.install_date || '--' }}
            </view>
            <!-- <view class="bottom-1px">备注：{{ info.remark || '--' }}</view> -->
          </view>
        </wd-collapse-item>
        <wd-collapse-item class="content-wrap" title="整改记录" name="item2">
          <!-- :data="recordList" -->
          <wd-steps :active="recordList?.length" vertical dot>
            <wd-step
              v-for="(item, index) in recordList"
              :key="index"
              :title="item.nodeName"
              :description="item.nodeName"
            >
              <template #description>
                <view
                  v-for="(node, indexSon) in item.nodeInfo"
                  :key="indexSon"
                  :style="{ color: node.color || '' }"
                  class="text-14px mb-5px"
                >
                  <view v-if="node.webType === 'image'">
                    {{ node.description }}：
                    <wd-img
                      class="image-content"
                      :src="getFullThumbnailUrl(node.dataValue[0], '100x100')"
                      :enable-preview="true"
                    />
                  </view>
                  <view v-if="node.webType.toLowerCase() === 'string'">
                    {{ toText(node.description) }}：{{ node.dataValue }}
                  </view>
                </view>
              </template>
            </wd-step>
          </wd-steps>
        </wd-collapse-item>
        <wd-collapse-item class="content-wrap" title="催促信息" name="item3">
          <view v-for="(item, index) in urgeList" :key="index" class="mb-10px urge-content">
            <view class="mb-10px content-item">催促人：{{ item.operatorName || '--' }}</view>
            <view class="mb-10px content-item">催促时间：{{ item.createTime || '--' }}</view>
          </view>
          <view v-if="!urgeList.length">暂无数据~</view>
        </wd-collapse-item>
      </wd-collapse>
    </view>
    <!-- 底部按钮 -->

    <view class="bottom-content" v-if="+info?.disposeState === 0">
      <wd-button @click="handleSelf" class="bottom-btn">自行整改</wd-button>
    </view>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store'
import SafetyNavbar from '@/components/safety-navbar.vue'
import { currRoute } from '@/utils'
import { getFullThumbnailUrl } from '@/utils/fileUrl'
import { dataList } from './mock'
import { normalizeAddress } from '@/utils/resFormatter'
import { getWarnExtDetailAPI, geturgeListAPI, getRecordListAPI } from './fetch'

const abnormalRecord = ref([])
const { id } = currRoute().query
const { userInfo } = useUserStore()
const recordList = ref([])
const urgeList = ref([])
const warnInfoList = ref() as any
const collapseValue = ref(['item0', 'item1', 'item2', 'item3', 'item4'])

const info = ref()

function gethdSource(type) {
  if (+type === 1) return '物联网监测'
  if (+type === 2) return '人工主动上报'
  if (+type === 3) return '智能视频终端'
  if (+type === 4) return '监督检查隐患'
  if (+type === 5) return '巡检巡查'
}

// 整改记录
async function getRecordList() {
  const res: any = await getRecordListAPI({
    disposeId: info.value.disposeId,
    eventType: '4',
  })
  if (res.code === 'success') recordList.value = res.data.rows
}

function toText(e: string): string {
  switch (e) {
    case '处置时间':
      return '整改时间'
    case '处置描述':
      return '整改描述'
    case '平台首次接收时间':
      return '上报时间'
    default:
      return e
  }
}

function viewLocation() {
  uni.navigateTo({
    url: `/pages/inspectionDetail/alarmListItem?id=${info.value.id}&eventType=4&deviceId=${info.value.deviceInfo.deviceId}`,
  })
}

// 异常记录
function queryHazardMergerRecord() {
  abnormalRecord.value = dataList
  console.log('abnormalRecord.value -----> 🚀', abnormalRecord.value)
  abnormalRecord.value.forEach(function (item) {
    item.dataList = []
    if (item.lastEventTime) {
      item.dataList.push({
        label: '最近一次',
        desc: item.lastEventDesc,
        value: item.lastMonitorValue,
        unit: item.lastMonitorUnit,
        time: item.lastEventTime,
      })
    }
    if (item.firstEventTime) {
      item.dataList.push({
        label: '首次',
        desc: item.firstEventDesc,
        value: item.firstMonitorValue,
        unit: item.firstMonitorUnit,
        time: item.firstEventTime,
      })
    }
    item._deviceAddress = normalizeAddress(item)
  })
}

// 催促信息
async function geturgeList() {
  const res: any = await geturgeListAPI({ disposeId: info.value.disposeId })
  if (res.code === 'success') urgeList.value = res.data
}

// 隐患信息
async function getWarnExtDetail() {
  const res: any = await getWarnExtDetailAPI({ id: currRoute().query.id })
  if (res.code === 'success') warnInfoList.value = res.data
}

const handleSelf = () => {
  console.log('info.value', info.value)
  const encodedList = encodeURIComponent(JSON.stringify(info.value))
  uni.navigateTo({
    url: `/pages/inspectionDetail/misinformation?list=${encodedList}&isBatch=false`,
  })
}

async function getData() {
  const res: any = await getWarnExtDetailAPI({ id: currRoute().query.id })
  console.log('res', res)

  info.value = res.data

  getRecordList()
  geturgeList()
  getWarnExtDetail()
  queryHazardMergerRecord()
}

onMounted(() => {
  // getEventRecordInfo()
  getData()
})
</script>

<style lang="scss" scoped>
.urge-content {
  border-bottom: #ddd 1px dashed;
  &:last-child {
    border-bottom: none;
  }
}
.scroll-content {
  @apply flex-1;
  overflow-y: auto;
}

.bottom-content {
  display: grid;
  grid-template-columns: 1fr;
  align-items: center;
  width: 100%;
  height: 61px;
  background-color: #fff;
  .check-box {
    justify-self: center;
  }
}
.bottom-card {
  height: auto;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}

.content-wrapTop {
  top: 10px;
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: rgb(235, 238, 245);
  border-radius: 10px;
}
.content-wrap {
  box-sizing: border-box;
  width: 100%;
  // padding: 10px;
  background-color: rgb(235, 238, 245);
  border-radius: 10px;
  .image-content {
    overflow: hidden;
    vertical-align: top;
    border-radius: 5px;
  }
}
.bg {
  background-color: rgb(235, 238, 245);
}

:deep(.wd-collapse-item) {
  margin-bottom: 10px;
}

.yicang-content {
  margin-bottom: 15px;
  overflow: hidden;
  color: #000;
  background-color: #fff;
  border-radius: 10px;
  .title {
    padding: 10px;
    color: #fff;
    background-color: #0088ff;
  }
  .content {
    padding: 10px;
  }
  .bottom {
    padding: 10px;
    color: red;
    text-align: center;
    border-top: #ddd 1px solid;
  }
  .abnormal-circle {
    .label {
      font-weight: 600;
      color: #000;
    }
    .desc {
      font-weight: 500;
      color: #000;
    }
  }
}
.bottom-btn {
  margin: auto;
}
:deep(.box-border) {
  padding-top: 0 !important;
}
</style>
