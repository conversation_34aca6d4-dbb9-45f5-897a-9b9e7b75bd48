import { http } from '@/utils/http'
import { api } from '@/api'

const SERVER: string = import.meta.env.VITE_HAZARD_PORT

// 设备信息
export const getWarnExtDetailAPI = (param: any) => {
  const url = api.getUrl(api.type.hazard, api.name.hazard.qeuryEventDetail)
  return http.post<any>(`${url}?id=${param.id}`)
}

// 整改记录
export const getRecordListAPI = (param: any) => {
  const url = api.getUrl(api.type.hazard, api.name.hazard.getRecord)
  return http.post(url, param)
}

// 催促信息
export const geturgeListAPI = (param: any) => {
  const url = api.getUrl(api.type.hazard, api.name.hazard.getUrgeRecord)
  return http.post(url, param)
}

// export const getItemInfoFireAPI = (param: any) => {
//   return http.post<any>(`${SERVER}/alarmRecord/getAlarmPageList`, { ...param })
// }

// // 告警记录列表 - 火警
// export const getAlarmPageListAPI = (param: any) => {
//   return http.post<any>(
//     `${SERVER}/alarmRecord/getAlarmPageList?orgCode=${param.orgCode}&sysCode=${param.sysCode}&disposeId=${param.disposeId}&showRisk=${param.showRisk}`,
//   )
// }

// // 预警记录列表
// export const getWarningPageListAPI = (param: any) => {
//   return http.post<any>(
//     `${SERVER}/warningRecord/getWarningPageList?orgCode=${param.orgCode}&sysCode=${param.sysCode}&disposeId=${param.disposeId}`,
//   )
// }

// // 故障记录列表
// export const getFaultPageListAPI = (param: any) => {
//   return http.post<any>(
//     `${SERVER}/warningRecord/getFaultPageList?orgCode=${param.orgCode}&sysCode=${param.sysCode}&disposeId=${param.disposeId}`,
//   )
// }

// // 行为记录列表
// export const getActionPageListAPI = (param: any) => {
//   return http.post<any>(
//     `${SERVER}/actionRecord/getActionPageList?orgCode=${param.orgCode}&sysCode=${param.sysCode}&disposeId=${param.disposeId}`,
//   )
// }
