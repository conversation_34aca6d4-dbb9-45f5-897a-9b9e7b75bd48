<template>
  <view v-if="accidentList.length > 0">
    <view
      class="accident-content"
      v-for="item in accidentList"
      :key="item.id"
      @click="handleDetail(item)"
    >
      <view class="accident-card">
        <view class="header">
          <view class="title">{{ item.planName }}</view>
          <view>
            <view class="status" :style="{ 'background-color': getStatusColor(item.taskStatus) }">
              {{ item.taskStatusName }}
            </view>
          </view>
        </view>

        <view class="content">
          <!-- 图片展示区域 -->
          <template v-if="item.taskStatus === 0 || item.taskStatus === 1">
            <view class="no-start">
              <image class="no-start-image" src="../assets/item-no-start.png" mode="aspectFill" />
            </view>
          </template>
          <template v-else>
            <view class="image-group">
              <view v-for="i in 6" :key="i" class="image-item">
                <template v-if="item.videoUrls && item.videoUrls.length > 6 && i === 6">
                  <view class="more-image" @click.stop="toDetail(item)">
                    <text class="more-text">+{{ item.videoUrls.length - 6 }}</text>
                  </view>
                </template>
                <template v-else-if="item.videoUrls && item.videoUrls[i - 1]">
                  <image
                    class="thumbnail"
                    :src="getFullThumbnailUrl(item.videoUrls[i - 1], '126x90')"
                    mode="aspectFill"
                    @click.stop="previewImage(item.videoUrls, i - 1)"
                  />
                </template>
                <template v-else>
                  <image
                    class="thumbnail no-image"
                    src="../assets/item-no-pic.png"
                    mode="aspectFill"
                  />
                </template>
              </view>
            </view>
          </template>

          <view class="info-section">
            <view class="time-range">巡检时间：{{ getFormatTimeRange(item) }}</view>

            <view class="stats-section">
              <text class="abnormal">异常情况：{{ getAbnormalDisplay(item) }}</text>
              <text class="video-points">视频点位：{{ item.videoDeviceNum }}个</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view v-else>
    <Empty />
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { replaceWithThumbnailPath } from '@/utils/index'
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl'
import Empty from '@/components/empty.vue'

defineProps({
  accidentList: {
    type: Array as any,
    default: () => {
      return []
    },
  },
})
const Emits = defineEmits(['detail'])

// 定义状态颜色配置
const STATUS_CONFIG = {
  0: { label: '待开始', color: '#ffc107' }, // 黄色
  2: { label: '进行中', color: '#2196f3' }, // 蓝色
  3: { label: '已完成', color: '#4caf50' }, // 绿色
} as const

const getStatusColor = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.color || '#9e9e9e'
}

const getFormatTimeRange = (item: any): string => {
  const startTime = item.taskPlanStartTime
  const endTime = item.taskPlanEndTime

  if (!startTime) {
    return '--'
  }

  if (!endTime) {
    return startTime
  }

  const endTimeOnly = endTime.split(' ')[1] || ''

  if (!endTimeOnly) {
    return startTime
  }

  return `${startTime} ~ ${endTimeOnly}`
}

const getAbnormalDisplay = (item: any): string => {
  if (item.taskStatus == 2 || item.taskStatus == 3) {
    return `${item.abnormalNum || 0}个`
  }
  return '--'
}

const previewImage = (urls: string[], currentIndex: number) => {
  if (!urls || urls.length === 0) return

  const fullUrls = urls.map((url) => getFullFileUrl(url))

  uni.previewImage({
    current: currentIndex,
    urls: fullUrls,
    indicator: 'number',
    loop: true,
  })
}

// 查看详情
const toDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/inspectTaskDetail/inspectTaskDetail?id=${item.taskId}`,
  })
}

const handleDetail = (value: any) => {
  console.log(value, 'value')
  uni.navigateTo({
    url: `/pages/accidentDetail/accidentDetail?id=${value.taskId}`,
  })
}
</script>

<style lang="scss" scoped>
.accident-content {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .accident-card {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    padding: 12px;
    background: white;
    border-radius: 6px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 12px;

      .title {
        flex: 1;
        margin-right: 10px;
        overflow: hidden;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .status {
        flex-shrink: 0;
        min-width: 60px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
        color: white;
        text-align: center;
        border-radius: 12px;
      }
    }

    .content {
      .no-start {
        width: 100%;
        height: 120px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 4px;

        .no-start-image {
          width: 100%;
          height: 100%;
        }
      }

      .image-group {
        display: grid;
        grid-template-rows: repeat(2, 80px);
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        margin-bottom: 12px;

        .image-item {
          overflow: hidden;
          background-color: #f5f5f5;
          border-radius: 4px;

          .thumbnail {
            width: 100%;
            height: 100%;
            border-radius: 4px;

            &.no-image {
              opacity: 0.6;
            }
          }

          .more-image {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            border: 1px solid #ddd;

            .more-text {
              font-size: 16px;
              font-weight: bold;
              color: white;
            }
          }
        }
      }

      .info-section {
        .stats-section {
          display: flex;
          justify-content: space-between;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
