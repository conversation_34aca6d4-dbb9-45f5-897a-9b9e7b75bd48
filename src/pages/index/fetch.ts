import { http } from '@/utils/http'
import { api } from '@/api'

// 巡检任务列表
export const getPageViewTaskAPI = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.pageViewTask)
  return http.post(url, { ...param })
}

// // 事故类型
// export const getCategory = (param: any) => {
//   return http.get<any>(`${SERVER}/category/list`, { ...param })
// }

// // 事故等级
// export const getLevel = (param: any) => {
//   return http.get<any>(`${SERVER}/level/list`, { ...param })
// }

// // 设备信息
export const getVideoDeviceInfoAPI = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.getVideoDeviceInfo)
  return http.get(url, { ...param })
}

// // 历史事件
export const queryVideoTaskDisposeByVideoDeviceIdAPI = (param: any) => {
  const url = api.getUrl(
    api.type.intelligent,
    api.name.inspectTask.queryNewVideoTaskDisposeByVideoDeviceId,
  )
  return http.post(url, { ...param })
}
