<template>
  <view class="container">
    <!-- loading组件 -->
    <view class="loading-mask" v-if="showLoading">
      <view class="loading-wrapper">
        <uni-loading type="circle" size="24"></uni-loading>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { wvSubscribe, beforeEnter } from '@/hooks/wvSubscribe'

const showLoading = ref(true)

onLoad(async () => {
  await beforeEnter()
  await wvSubscribe()
  showLoading.value = false
  uni.redirectTo({
    url: `/pages/home/<USER>
  })
})
</script>

<style scoped>
.container {
  position: relative;
  min-height: 100vh;
}

.center {
  padding: 20px;
  text-align: center;
}

.loading-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.loading-text {
  font-size: 14px;
  color: #333;
}
</style>
