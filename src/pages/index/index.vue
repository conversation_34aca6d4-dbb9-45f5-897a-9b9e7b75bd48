<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '智能巡检任务',
  },
}
</route>
<template>
  <z-paging class="wrapper" ref="paging" v-model="accidentLists" @query="getList">
    <template #top>
      <SafetyNavbar title="智能巡检任务"></SafetyNavbar>
      <wd-search
        placeholder-left
        @change="handleSearchChange"
        placeholder="请输入任务名称模糊搜索"
        :hide-cancel="true"
        v-model="searchParams.planName"
        @clear="handleSearchClear"
      />

      <wd-drop-menu style="z-index: 99">
        <wd-drop-menu-item
          title="巡检状态"
          v-model="searchParams.taskStatus"
          :options="inspectionStatusList"
          @change="handleStatusChange"
        />
        <wd-drop-menu-item
          title="巡检结果"
          v-model="searchParams.videoResult"
          :options="inspectionResultList"
          @change="handleResultChange"
        />
      </wd-drop-menu>
    </template>
    <accident-list :accidentList="accidentLists" />
  </z-paging>
</template>

<script lang="ts" setup>
import SafetyNavbar from '@/components/safety-navbar.vue'
import accidentList from './components/accidentList.vue'
import { getPageViewTaskAPI } from './fetch'
import { useUserStore } from '@/store/index'
import { ref } from 'vue'

const paging = ref()
const accidentLists = ref<any[]>([])

const userInfo = useUserStore().userInfo
console.log(userInfo, 'userInfo')

const searchParams = ref<any>({
  planName: '',
  taskStatus: null,
  videoResult: null,
}) // 搜索参数

// 巡检状态
const taskStatus = ref('')
const inspectionStatusList = ref([
  { label: '全部', value: '' },
  { label: '待开始', value: '0' },
  { label: '未执行/逾期', value: '1' },
  { label: '进行中', value: '2' },
  { label: '已完成', value: '3' },
])

// 巡检结果
const inspectionResult = ref('')
const inspectionResultList = ref([
  { label: '全部', value: '' },
  { label: '正常', value: '0' },
  { label: '异常', value: '2' },
])
const param = ref({
  pageNo: 1,
  pageSize: 10,
  createdBy: userInfo.id,
  createdByName: userInfo.userName,
  unitId: userInfo.orgCode,
  zhId: userInfo.zhId,
})

const getList = async (pageNo: number) => {
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    param.value.pageNo = pageNo
    const res = await getPageViewTaskAPI({ ...param.value, ...searchParams.value })

    if (res && res.data) {
      paging.value.complete(res.data.rows || [])
    } else {
      paging.value.complete([])
    }

    uni.hideLoading()
  } catch (error) {
    console.error('获取列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

// 搜索变化
const handleSearchChange = (value: string) => {
  searchParams.value = {
    ...searchParams.value,
    planName: value.value,
  }
  paging.value.reload()
}

// 清除搜索
const handleSearchClear = () => {
  searchParams.value = { ...searchParams.value }
  paging.value.reload()
}

// 状态变化
const handleStatusChange = (event: any) => {
  console.log(event, 'event')
  const value = event.selectedItem.value
  searchParams.value = {
    ...searchParams.value,
    taskStatus: value,
  }
  paging.value.reload()
}

// 结果变化
const handleResultChange = (event: any) => {
  const value = event.selectedItem.value
  searchParams.value = {
    ...searchParams.value,
    videoResult: value,
  }
  paging.value.reload()
}

// onLoad(() => {
//   console.log('组件挂载，开始加载数据')
//   nextTick(() => {
//     setTimeout(() => {
//       const userInfo = useUserStore().userInfo
//       console.log(userInfo, 'userInfo')
//     }, 500)
//   })
//   // paging.value?.reload()
// })

// watch(
//   () => useUserStore().userInfo.id,
//   (val) => {
//     console.log(val, 'val>>>>>>')
//     // paging.value?.reload()
//     if (val) {
//       nextTick(() => {
//
//         // paging.value?.reload()
//       })
//     }
//   },
//   {
//     deep: true,
//   },
// )

onShow(() => {
  console.log('页面显示，重新加载数据')
  // paging.value?.reload()
})
</script>

<style lang="scss" scoped>
::v-deep {
  // .wd-search {
  //   background: none !important;
  //   border: none !important;
  // }

  .zp-page-top {
    background: white !important;
  }

  .navbar {
    position: unset !important;
  }
}
.wrapper {
  box-sizing: border-box;
  min-height: 100vh;
  // padding: 10px;
  background-color: #f7f7f7;
}

// 确保下拉菜单样式正确
:deep(.wd-drop-menu) {
  z-index: 99;
}

:deep(.wd-drop-menu-item) {
  z-index: 100;
}
</style>
