<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '查看位置',
  },
}
</route>
<template>
  <SafetyNavbar title="查看位置"></SafetyNavbar>
  <view class="device-location">
    <FloorGis
      v-if="floorId"
      :type="gisType"
      :build-id="buildId"
      :floor-id="floorId"
      :device-list="deviceList"
    />
  </view>
</template>

<script lang="ts" setup>
import FloorGis from '@/gis-floor/floorGis.vue'
import { EGisType } from '@/gis-floor/constant'
import { getDevicelnfoByld } from './fetchData'
import { useRoute } from 'vue-router'
import SafetyNavbar from '@/components/safety-navbar.vue'

const route = useRoute()

const gisType = EGisType.DEVICELOC

const buildId = ref('')
const floorId = ref('')
const deviceList = ref<any[]>([])

const getData = () => {
  getDevicelnfoByld(route.query.deviceId || '').then((res) => {
    if (res.data.length > 0) {
      const data = res.data[0]
      deviceList.value = [
        {
          deviceId: data.deviceId,
          deviceNum: data.deviceNum,
          deviceTypeId: data.deviceTypeId,
          deviceAddress: data.deviceAddress,
          buildingId: data.buildingId,
          floorId: data.floorId,
          mapX: data.mapX,
          mapY: data.mapY,
          mapZ: data.mapZ,
          latitude: data.latitude,
          longitude: data.longitude,
        },
      ]

      buildId.value = deviceList.value[0]?.buildingId
      floorId.value = deviceList.value[0]?.floorId
    }
  })
}
getData()

defineOptions({ name: 'InspectPath' })
</script>

<style lang="scss" scoped>
::v-deep {
  // .zp-page-top {
  //   background: white !important;
  // }
  // .navbar {
  //   position: unset !important;
  // }
}
.device-location {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #0c1d3e;
}
</style>
