<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '智能巡检详情',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="historyList" @query="gethistoryList" :refresher-enabled="false">
    <template #top>
      <SafetyNavbar :title="title"></SafetyNavbar>
      <view class="video">
        <!-- 'https://wy-hikvision.tanzervas.com:6014/openUrl/vsigdrGlhza3f168503dd7a4b019df62/live.m3u8' -->
        <iframe
          :src="getVideoPlayerUrl(info.videoPlayUrl, undefined, { autoplay: true })"
          class="w-full h-full"
          frameborder="0"
          allow="autoplay; fullscreen"
        ></iframe>
      </view>
    </template>
    <view class="container">
      <view class="infoview">
        <view class="text-[#212121FF] text-[16px] font-[500]">基本信息</view>
        <view v-for="(itme, index) in infoParam" :key="index">
          <view v-if="itme.key == 'status'" class="info-item">
            {{ itme.lable }}
            <view v-if="info.erecordDeviceInfo?.[itme.key] == '1'" class="text-[#24A143FF]">
              在线
            </view>
            <view v-if="info.erecordDeviceInfo?.[itme.key] == '0'" class="text-[#232323FF]">
              离线
            </view>
          </view>
          <view v-else class="info-item">
            {{ itme.lable }}
            {{ info.erecordDeviceInfo?.[itme.key] || '' }}
            <wd-button
              class="ml-[10px]"
              v-if="itme.key == 'buildingName'"
              size="small"
              plain
              @click="toLocation"
            >
              查看位置
            </wd-button>
          </view>
        </view>
      </view>
      <view class="infoview">
        <view class="text-[#212121FF] text-[16px] font-[500]">历史事件</view>
        <view
          class="flex justify-between mt-[16px] pb-[16px]"
          style="border-bottom: 1px solid #e9ebf0ff"
          v-for="($item, index) in historyList"
          :key="index"
        >
          <view @click="goAccidentDetail($item)">
            <view class="flex items-center">
              <view class="text-[#212121FF] text-[14px] font-[500]">{{ $item.eventDesc }}</view>

              <view
                class="w-[44px] h-[19px] text-[#FFFFFFFF] text-[12px] font-[400] rounded-[4px] text-center ml-[10px]"
                :style="{ background: disposeStatusObj[+$item.disposeState]?.color }"
              >
                {{ disposeStatusObj[+$item.disposeState]?.lable }}
              </view>
            </view>
            <view class="text-[#999999FF] text-[13px] font-[400] mt-[10px]">
              {{ $item.eventTime }}
            </view>
          </view>
          <view>
            <wd-img
              class="w-[78px] h-[44px]"
              :src="getFullThumbnailUrl($item.picUrl[0], '_78X44.jpg')"
              @click="previewImage($item.picUrl, 0)"
            />
            <wd-icon name="arrow-right" color="#666666FF" size="22px"></wd-icon>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
  <comOverlay
    ref="overlay"
    v-if="previewVisible"
    :image-list="previewList"
    :initial-index="currentIndex"
    @close="closePreview"
  />
</template>
<script lang="ts" setup>
import SafetyNavbar from '@/components/safety-navbar.vue'
import { getVideoDeviceInfoAPI, queryVideoTaskDisposeByVideoDeviceIdAPI } from '../index/fetch'
import { getVideoPlayerUrl } from '@/utils/player'
import { useUserStore } from '@/store'
import { DeviceInfo, DisposeStatus, HistoryItem, InfoParam } from './type'
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/index'
import comOverlay from '@/components/com-overlay.vue'
import { getHazardIdAPI } from '../accidentDetail/fetch'

// 预览状态
const previewVisible = ref(false)
const previewList = ref<string[]>([])
const currentIndex = ref(0)
// 预览图片
const previewImage = (data: any[], index: number) => {
  previewList.value = data.map((item) => getFullFileUrl(item, ''))
  currentIndex.value = index
  previewVisible.value = true
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
}
const { userInfo } = useUserStore()

const title = ref('详情')
// 1：解决，2：未解决
const disposeStatusObj: DisposeStatus[] = [
  { lable: '待处置', color: '#F56C6C' },
  { lable: '已处置', color: '#67C23A' },
  { lable: '处置中', color: '#E6A23C' },
  // { lable: '已处置', color: '#67C23A' },
  // { lable: '未处置', color: '#F56C6C' },
]
const info = ref<DeviceInfo | null>({})
const historyList = ref<HistoryItem[]>([])

const param = ref({
  pageNo: 1,
  pageSize: 10,
  deviceId: '',
  zhId: userInfo?.zhId,
  createdBy: userInfo?.createdBy,
})
const infoParam = ref<InfoParam[]>([
  { lable: '单位：', key: 'unitName' },
  { lable: '位置：', key: 'buildingName' },
  { lable: '品牌型号：', key: 'brand' },
  { lable: '设备编号：', key: 'deviceId' },
  { lable: '状态：', key: 'status' },
])

function getInfo() {
  getVideoDeviceInfoAPI({
    deviceId: param.value.deviceId,
    zhId: param.value.zhId,
    createdBy: param.value.createdBy,
  }).then((res: any) => {
    console.log(res)
    info.value = res.data
  })
}
onLoad((options) => {
  param.value.deviceId = options.deviceId || '20250528111122411625'
  title.value = options.loactionName
  getInfo()
})

const paging = ref<any>()

async function gethistoryList(pageNo: number) {
  console.log(param.value, '-=-=-=param.value')
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    param.value.pageNo = pageNo
    const res = await queryVideoTaskDisposeByVideoDeviceIdAPI(param.value)
    console.log('历史数据 res = ', res)
    if (res && res.data) {
      paging.value.complete(res.data.rows || [])
    } else {
      paging.value.complete([])
    }

    uni.hideLoading()
  } catch (error) {
    console.error('获取列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}
async function goAccidentDetail(item: any) {
  console.log(item)
  const CompMap = {
    1: ['fireDetails', '', ''],
    2: ['warningDetails', '', ''],
    3: ['faultDetails', 'detail', ''],
    4: ['hiddenDetails', '', 'id'],
    5: ['actionDetails', '', ''],
    6: ['offlineDetails', '', ''],
  }
  const { eventType, disposeState, disposeId, id } = item
  const Origin = window.location.origin
  let url: string
  const unitId = userInfo?.orgCode
  if (+disposeState === 0 && +eventType === 1) {
    // 火警 待处置
    url = `${Origin}/ehs-internet-monitor-h5/#/pages/event-handling/alarmDetails/${CompMap[+eventType][0]}/alarmListItem?disposeId=${disposeId}&eventType=${eventType}&unitId=${unitId}`
  } else {
    const res: any = await getHazardIdAPI({ id: disposeId })
    if (res.code === 'success') {
      url = `${Origin}/ehs-internet-monitor-h5/#/pages/event-handling/alarmDetails/${CompMap[+eventType][0]}/${CompMap[+eventType][1] || 'disposalDetail'}?${+eventType === 4 ? 'id' : 'disposeId'}=${res.data.id}`
    }
  }
  window.location.href = url
  // uni.navigateTo({
  //   url: `/pages/inspectionDetail/index?id=${item.disposeId}`,
  // })
}
const toLocation = () => {
  uni.navigateTo({
    url: `/pages/deviceLocation/index?deviceId=${param.value.deviceId || ''}`,
  })
}
</script>
<style lang="scss" scoped>
::v-deep {
  // .zp-page-top {
  //   background: white !important;
  // }
  .navbar {
    position: unset !important;
  }
}
.video {
  width: 100%;
  height: 25vh;
  // margin-top: 45px;
  // border: 1px solid red;
}
.infoview {
  width: 85%;
  padding: 15px;
  margin: auto;
  margin-top: 11px;
  background-color: white;
  border-radius: 6px 6px 6px 6px;
  .info-item {
    @apply flex items-center text-[#212121FF] text-[14px] font-[400] mt-[6px] gap-[6px];
  }
}
</style>
