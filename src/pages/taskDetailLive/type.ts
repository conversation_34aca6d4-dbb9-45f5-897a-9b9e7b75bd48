// 设备基本信息
export interface DeviceInfo {
  erecordDeviceInfo?: any
  ibmDeviceInfoVo?: any
  loopDeviceNum?: string
  videoPlayUrl?: string
  videoRelDeviceList?: any[]
}

// 事件列表
export interface HistoryItem {
  // eventTypeName: string
  // disposeStatus: string
  // deviceTime: string
  // videoUrl: string
  deviceId: string
  eventType: string
  picUrl: any[]
  eventDesc: string
  disposeId: string
  disposeState: string
  disposeStateName: string
  eventTime: string
  subCenterCode: string
  hazardEventId: string
  addrees: string
}

// 自定义类型
export interface InfoParam {
  lable: string
  key: string
}

// 自定义类型
export interface DisposeStatus {
  lable: string
  color: string
}
