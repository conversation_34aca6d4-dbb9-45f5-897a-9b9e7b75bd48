<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '事故详情',
    navigationBarBackgroundColor: '#0087fc',
    navigationBarTextStyle: 'white',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="tableData" @query="getList">
    <template #top>
      <SafetyNavbar title="智能巡检任务"></SafetyNavbar>
    </template>
    <view class="accident-detail">
      <CustomTabs
        :tabs="tabs"
        class="!mt-[40px]"
        :activeIndex="activeIndex"
        @handleClick="handleChange"
      ></CustomTabs>

      <Detail
        @getCardStatus="getCardStatus"
        :tableData="tableData"
        :info="accidentInfo"
        v-if="activeIndex == 0"
      />
      <Manifest :tableData="tableData" :info="accidentInfo" @search="handleManifestSearch" v-else />
    </view>

    <template #bottom v-if="activeIndex == 0">
      <PathBtn :unit-id="unitId" :task-id="tId" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { getAccidentDetail, taskInspectionDetailAPI, taskInspectionPageAPI } from './fetch'
import SafetyNavbar from '@/components/safety-navbar.vue'
import CustomTabs from './components/custom-Tabs.vue'
import Detail from './components/detail.vue'
import Manifest from './components/manifest.vue'
import { useUserStore } from '@/store/index'
import PathBtn from './components/inspectPathBtn.vue'

const tabs = ref(['任务详情', '事件清单'])

const activeIndex = ref(0)
const userInfo = useUserStore().userInfo
function handleChange(event: number) {
  activeIndex.value = event
  manifestSearchParams.value.inspectionResult = ''
  manifestSearchParams.value.taskStatus = ''
  manifestSearchParams.value.startTime = ''
  manifestSearchParams.value.endTime = ''
  // 切换tab时重新加载数据
  paging.value?.reload()
}

const paging = ref()
const tableData = ref([])

const cardStatus = ref<string | number>('1')

const getCardStatus = (num: number) => {
  cardStatus.value = num
  paging.value?.reload()
}

const param = ref({
  pageNo: 1,
  pageSize: 10,
  zhId: userInfo.zhId,
  createdBy: userInfo.id,
})

// 事件清单搜索参数
const manifestSearchParams = ref({
  taskStatus: '',
  inspectionResult: '',
  startTime: '',
  endTime: '',
})

const getList = async (pageNo: number) => {
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    param.value.pageNo = pageNo

    if (activeIndex.value === 0) {
      const res = await taskInspectionDetailAPI({
        ...param.value,
        taskId: tId.value,
        inspectionStatus: cardStatus.value,
      })
      if (res && res.data) {
        paging.value.complete(res.data.rows || [])
      } else {
        paging.value.complete([])
      }
    } else {
      // 事件清单tab - 调用事件清单接口，包含搜索参数
      const searchParams = {
        ...param.value,
        taskId: tId.value,
        eventType: manifestSearchParams.value.taskStatus,
        disposeStatus: manifestSearchParams.value.inspectionResult,
        startTime: manifestSearchParams.value.startTime || '',
        endTime: manifestSearchParams.value.endTime || '',
      }

      const res = await taskInspectionPageAPI(searchParams)
      if (res && res.data) {
        paging.value.complete(res.data.rows || [])
      } else {
        paging.value.complete([])
      }
    }

    uni.hideLoading()
  } catch (error) {
    console.error('获取列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

// 处理事件清单搜索
const handleManifestSearch = (searchParams: any) => {
  manifestSearchParams.value = {
    taskStatus: searchParams.taskStatus,
    inspectionResult: searchParams.inspectionResult,
    startTime: searchParams.startTime,
    endTime: searchParams.endTime,
  }
  // 重新加载数据
  if (activeIndex.value === 1) {
    paging.value?.reload()
  }
}

const accidentInfo = ref<any>({})
const unitId = computed(() => accidentInfo.value.deptId || '')

const getDetailData = async (id: string) => {
  try {
    const res = await getAccidentDetail({ taskId: id })
    accidentInfo.value = res.data
  } catch (error) {}
}

const tId = ref('')
onLoad((o) => {
  tId.value = o.id
  getDetailData(o.id)
  paging.value?.reload()
})
</script>

<style lang="scss" scoped>
.accident-detail {
  overflow: auto;
  background-color: #f7f7f7;
}
.btn {
  position: fixed;
  bottom: 0;
  z-index: 10000;
  width: 100vw;
  color: white;
  background-color: #0256ff;
  border-radius: 0;
}
</style>
