import { http } from '@/utils/http'
import { api } from '@/api'

// 事故详情
export const getAccidentDetail = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.taskTopDetail)
  return http.get(url, { ...param })
}

export const taskInspectionDetailStatisticAPI = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.taskInspectionDetailStatistic)
  return http.post(url, { ...param })
}

// 巡检清单
export const taskInspectionPageAPI = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.getViewTaskDevice)
  return http.post(url, { ...param })
}

export const taskInspectionDetailAPI = (param: any) => {
  const url = api.getUrl(api.type.intelligent, api.name.inspectTask.taskInspectionDetail)
  return http.post(url, param)
}

// 查询全部类型
export const getQueryDictDataAPI = () => {
  const url = api.getUrl(api.type.intelligent, api.name.hazard.getQueryDictDataList)
  return http.post(url)
}
