<template>
  <view class="taskList" @click="toDetail(data)">
    <view class="head">
      <view class="point">
        <view>{{ padStart(index + '', 2, '0') || '00' }}</view>
        <view>检查点</view>
      </view>
      <view>{{ data.deviceAddress }}</view>
      <view class="status" :style="{ 'background-color': getStatusColor(data.videoResult) }">
        {{ getStatusLabel(data.videoResult) }}
      </view>
    </view>
    <view class="error" v-if="data.taskStatus == 1">
      异常事件：
      <text>{{ data.yichang }}</text>
    </view>
    <view class="img" style="padding: 12px">
      <view class="img-data" v-if="data.videoResult !== 1">
        <image class="image" :src="getFullFileUrl(data.videoDesc)" />
        <view class="time">巡检时间：{{ data.videoTime || '--' }}</view>
      </view>
      <view v-else class="no-data">
        <image src="../assets/noData.png" style="width: 169px; height: 118px" />
        <view class="text">任务开始后自动拍摄巡检截图</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { padStart } from 'lodash-es'
import { getFullFileUrl } from '@/utils/fileUrl'
import Empty from '@/components/empty.vue'

defineProps({
  index: Number,
  data: {
    type: Array as any,
    default: () => {
      return []
    },
  },
})

// 定义状态颜色配置
const STATUS_CONFIG = {
  0: { label: '正常', color: '#4caf50' }, // 绿色
  1: { label: '待巡检', color: '#ffc107' }, // 黄色
  2: { label: '异常', color: '#f91818' }, // 黄色
} as const
const getStatusColor = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.color
}
const getStatusLabel = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.label
}

const xjspjgMap = computed<Record<string, string>>(() => {
  const map: Record<string, string> = {}
  ;(xjspjgOpt.value || []).forEach((item) => {
    map[item.dictValue] = item.dictLabel
  })
  return map
})
function getVideoResultLabel(val: string) {
  return xjspjgMap.value[val] ?? '--'
}

// 查看详情
const toDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/ponitDetail/index?id=${item.taskId}&num=${item.positionNo}&total=${item.deviceTotal}&localtion=${item.deviceAddress}`,
  })
}
</script>

<style lang="scss" scoped>
.taskList {
  width: 95vw;

  margin-bottom: 10px;
  background: white;
  border-radius: 6px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    .point {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      color: white;
      text-align: center;
      background-color: #fbae49;
      border-radius: 5px;
    }
    .status {
      width: 44px;
      height: 19px;
      line-height: 19px;
      color: white;
      text-align: center;
      border-radius: 2px;
    }
  }
  .img {
    .img-data {
      position: relative;
      width: 100%;
      height: 180px;
      border-radius: 6px;
      .image {
        width: 100%;
        height: 100%;
        border-radius: 6px;
      }
      .time {
        position: absolute;
        bottom: 5px;
        width: 100%;
        height: 26px;
        padding-left: 5px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 0px 0px 6px 6px;
      }
    }
    .no-data {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 180px;
      background-color: #f8f9fa;
      border-radius: 6px;

      .text {
        margin-top: 5px;
        font-size: 12px;
        color: #9e9e9e;
      }
    }
  }
  .error {
    padding: 12px;
  }
}
</style>
