<template>
  <view class="w-full" v-if="hasGis">
    <button class="btn" @click="toGis">巡检路径</button>
  </view>
</template>

<script setup lang="ts">
import { getUnitInfo } from '@/gis-floor/floorGisService'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = withDefaults(
  defineProps<{
    unitId: string
    taskId: string
  }>(),
  {
    unitId: '',
    taskId: '',
  },
)

const hasGis = ref(false)

const getData = async () => {
  const _id = props.unitId
  const serviceModelCode: any = await getUnitInfo(_id)
  if (serviceModelCode !== 5) {
    hasGis.value = true
  }
}

const toGis = () => {
  uni.navigateTo({
    url: `/pages/inspectPath/index?taskId=${props.taskId || ''}`,
  })
}

watch(
  () => props.unitId,
  (nv) => {
    if (!nv) return
    getData()
  },
)
</script>

<style scoped lang="scss">
.btn {
  width: 100vw;
  color: white;
  background-color: #0256ff;
  border-radius: 0;
}
</style>
