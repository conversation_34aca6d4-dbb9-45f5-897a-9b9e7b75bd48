<template>
  <view class="manifest">
    <!-- 筛选条件 -->
    <wd-drop-menu>
      <wd-drop-menu-item
        title="全部类型"
        v-model="filterFrom.taskStatus"
        :options="eventTypeOptions"
        @change="emitSearch"
      />
      <wd-drop-menu-item
        title="处置类型"
        v-model="filterFrom.inspectionResult"
        :options="statusOptions"
        @change="emitSearch"
      />
      <view class="time-box" @click.stop="openPopup">
        <view>时间范围</view>
        <view>
          <img src="@/static/images/arrow.png" alt="" />
        </view>
      </view>
    </wd-drop-menu>

    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view>{{ (item as any)?.eventTypeName || '--' }}</view>
            <view class="event-tag" :class="getTagClass((item as any)?.disposeStatus)">
              {{ (item as any)?.disposeStatusName }}
            </view>
          </view>
          <view class="event-desc">
            {{
              (item as any)?.deviceAddress.length > 20
                ? (item as any)?.deviceAddress.substring(0, 20) + '...'
                : (item as any)?.deviceAddress || '--'
            }}
          </view>
          <view class="event-time">
            {{ (item as any)?.receiveTime || '--' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="getFullThumbnailUrl((item as any)?.videoUrl, '126x90')"
            mode="aspectFill"
            @click.stop="previewImage((item as any)?.videoUrl)"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 时间范围 -->
  <wd-calendar
    ref="calendar"
    :with-cell="false"
    type="daterange"
    v-model="timeValue"
    :max-range="360"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :close-on-click-modal="false"
  ></wd-calendar>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl'
import { getQueryDictDataAPI, getHazardIdAPI } from '../fetch'
import { useUserStore } from '@/store'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})
const { userInfo } = useUserStore()
const emit = defineEmits(['search'])
const eventTypeOptions = ref<{ label: string; value: string }[]>([])

// 处置状态选项
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '待处置', value: '0' },
  { label: '已处置', value: '1' },
  { label: '处置中', value: '2' },
])

const timeValue = ref<number[]>([])
const calendar = ref<any>()

// 搜索条件对象
const filterFrom = ref({
  taskStatus: '',
  inspectionResult: '',
  planStartTime: '',
  planEndTime: '',
})

async function getEventType() {
  const res: any = await getQueryDictDataAPI()
  eventTypeOptions.value = res.data.map((item) => {
    return {
      label: item.dictLabel,
      value: item.dictValue,
    }
  })
  eventTypeOptions.value.unshift({ label: '全部', value: '' })
}

function handleConfirm({ value }) {
  filterFrom.value.planStartTime = dayjs(value[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss')
  filterFrom.value.planEndTime = dayjs(value[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss')
  emitSearch()
}

function handleCancel() {
  timeValue.value = []
  filterFrom.value.planStartTime = ''
  filterFrom.value.planEndTime = ''
  emitSearch()
}

const previewImage = (url: string) => {
  if (!url) return

  const fullUrls = getFullFileUrl(url)

  uni.previewImage({
    urls: [fullUrls],
    current: fullUrls,
  })
}

function openPopup() {
  calendar.value?.open()
}

// 向父组件发送搜索条件
function emitSearch() {
  const searchParams = {
    taskStatus: filterFrom.value.taskStatus,
    inspectionResult: filterFrom.value.inspectionResult,
    startTime: filterFrom.value.planStartTime,
    endTime: filterFrom.value.planEndTime,
  }
  console.log('发送搜索条件:', searchParams)
  emit('search', searchParams)
}
// 获取事件类型标签样式
function getTagClass(eventType: string) {
  const typeMap: Record<string, string> = {
    0: 'tag-orange',
    1: 'tag-green',
    2: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
async function handleItemClick(item: any) {
  console.log('点击事件项:', item)
  if (!item.disposeId) return
  const CompMap = {
    1: ['fireDetails', '', ''],
    2: ['warningDetails', '', ''],
    3: ['faultDetails', 'detail', ''],
    4: ['hiddenDetails', '', 'id'],
    5: ['actionDetails', '', ''],
    6: ['offlineDetails', '', ''],
  }
  const { disposeEventType, disposeStatus, disposeId, id } = item
  const Origin = window.location.origin
  let url: string
  const unitId = userInfo?.orgCode
  if (+disposeStatus === 0 && +disposeEventType === 1) {
    // 火警 待处置
    url = `${Origin}/ehs-internet-monitor-h5/#/pages/event-handling/alarmDetails/${CompMap[+disposeEventType][0]}/alarmListItem?disposeId=${disposeId}&eventType=${disposeEventType}&unitId=${unitId}`
  } else {
    const res: any = await getHazardIdAPI({ id: disposeId })
    if (res.code === 'success') {
      url = `${Origin}/ehs-internet-monitor-h5/#/pages/event-handling/alarmDetails/${CompMap[+disposeEventType][0]}/${CompMap[+disposeEventType][1] || 'disposalDetail'}?${+disposeEventType === 4 ? 'id' : 'disposeId'}=${res.data.id}`
    }
  }
  window.location.href = url
  // uni.navigateTo({
  //   url: `/pages/inspectionDetail/index?id=${item.disposeId}&eventType=${item.eventType}`,
  // })
}

onLoad(() => {
  getEventType()
})
</script>

<style scoped lang="scss">
.manifest {
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: #f7f7f7;

  // 确保下拉菜单样式正确
  :deep(.wd-drop-menu) {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 34rpx;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.wd-drop-menu__list) {
    flex: 1;
  }

  // 时间选择器样式
  .time-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 240rpx;
    height: 44px;
    padding: 0 16rpx;
    font-size: 14px;
    color: #333;
    text-align: center;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }

    > view:first-child {
      flex: 1;
      text-align: center;
    }

    > view:last-child {
      position: absolute;
      right: 16rpx;
      bottom: 18rpx;
      scale: 0.7;
    }

    img {
      width: 12px;
      height: 12px;
      transition: transform 0.2s;
    }
  }

  .event-list {
    flex: 1;
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      margin-bottom: 12px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          display: flex;
          margin-bottom: 20rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #212121;

          .event-tag {
            width: 88rpx;
            height: 38rpx;
            margin-left: 24rpx;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 38rpx;
            color: #ffffff;
            text-align: center;
            border-radius: 4px;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #00bfa6;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          margin-bottom: 16rpx;
          font-size: 26rpx;
          font-weight: 400;
          line-height: 1.4;
          color: #808080;
        }

        .event-time {
          font-size: 26rpx;
          font-weight: 400;
          color: #999999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 166rpx;
          height: 108rpx;
          margin-right: 30rpx;
          border-radius: 4px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    padding: 40px 0;
    font-size: 14px;
    color: #999;
    text-align: center;
  }
}
</style>
