<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '巡检路径',
    navigationBarBackgroundColor: '#0087fc',
    navigationBarTextStyle: 'white',
  },
}
</route>
<template>
  <view class="inspect-path">
    <FloorGis
      v-if="floorId"
      :type="gisType"
      :build-id="buildId"
      :floor-id="floorId"
      :default-inspect-list="inspectList"
    />
  </view>
</template>

<script lang="ts" setup>
import FloorGis from '@/gis-floor/floorGis.vue'
import { EGisType } from '@/gis-floor/constant'
import { getDeviceListByTask } from './fetchData'
import { useRoute } from 'vue-router'

const route = useRoute()

const gisType = EGisType.INSPECTDETAIL

const buildId = ref('')
const floorId = ref('')
const inspectList = ref<any[]>([])

const getData = () => {
  getDeviceListByTask({
    pageNo: 1,
    pageSize: -1,
    taskId: route.query.taskId || '',
  }).then((res) => {
    if (res.data.length > 0) {
      const _list = res.data.reduce((preValue: any[], item) => {
        return [...preValue, ...item.deviceList]
      }, [])
      inspectList.value = _list.map((item) => ({
        ...item,
        deviceId: item.videoId,
        deviceName: item.videoName,
      }))
      inspectList.value.sort((a, b) => a.videoSort - b.videoSort)

      buildId.value = inspectList.value[0]?.buildingId
      floorId.value = inspectList.value[0]?.floorId
    }
  })
}
getData()

defineOptions({ name: 'InspectPath' })
</script>

<style lang="scss" scoped>
.inspect-path {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #0c1d3e;
}
</style>
