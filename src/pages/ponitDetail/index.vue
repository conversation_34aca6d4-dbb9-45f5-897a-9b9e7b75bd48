<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '巡检详情',
  },
}
</route>
<template>
  <view class="detail">
    <SafetyNavbar :title="title"></SafetyNavbar>
    <view class="ponitDetail">
      <!-- 有图片的情况 -->
      <view class="img" v-if="detailData.videoUrl !== ''">
        <view>巡检时刻截图</view>
        <image
          :src="getFullFileUrl(detailData.videoUrl)"
          @click="showZoomedImage(getFullFileUrl(detailData.videoUrl))"
          mode="widthFix"
        />
      </view>

      <!-- 无图片的情况 -->
      <view v-else class="no-img">
        <image src="./assets/card-no-pic.png" />
      </view>
      <view class="card1">
        <view class="title">
          <view>巡检结果</view>
          <view class="video" @click="toVideo">实时视频</view>
        </view>
        <view
          class="list"
          v-if="detailData.inspectionResult && detailData.inspectionResult.length !== 0"
        >
          <view v-for="item in detailData.inspectionResult" :key="item.id" class="li">
            <view>
              {{ item.eventTypeName }}
              <text>{{ item.videoResultName }}</text>
            </view>
          </view>
        </view>
        <view v-else>--</view>
      </view>
      <view class="card2">
        <view>巡检基本信息</view>
        <view class="info">
          <view>巡检时间：{{ detailData?.videoTime || '--' }}</view>
          <view>设备位置：{{ detailData?.deviceAddress || '--' }}</view>
          <view>品牌型号：{{ detailData?.brand || '--' }}</view>
          <view>设备编号：{{ detailData?.deviceNum || '--' }}</view>
        </view>
      </view>
      <!-- 图片放大弹窗 -->
      <view class="image-zoom-modal" v-if="zoomedImageUrl" @click="closeZoomedImage">
        <image :src="zoomedImageUrl" class="zoomed-image" mode="contain" @click.stop />
      </view>
    </view>
    <SafetyNavbar title="智能巡检任务"></SafetyNavbar>
    <view class="btn">
      <wd-button @click="handlePrev" :disabled="curPositionNo == 1" class="btn1">
        上一个点位
      </wd-button>
      <wd-button :disabled="curPositionNo == zongNum" @click="handleNext" class="btn2">
        下一个点位
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SafetyNavbar from '@/components/safety-navbar.vue'
import { taskPointDetailByPositionNoAPI } from './fetch.ts'
import { getFullFileUrl } from '@/utils/fileUrl'
import { useUserStore } from '@/store/index'

// 详情数据
const detailData = ref({})
// 放大的图片地址
const zoomedImageUrl = ref('')

const userInfo = useUserStore().userInfo

const tId = ref('')
const zongNum = ref('')
const curPositionNo = ref(1) // 当前 positionNo 位置， 从1起
const title = ref('')
onLoad((params) => {
  tId.value = params.id
  curPositionNo.value = Number(params.num)
  zongNum.value = Number(params.total) || 1
  title.value = params.localtion
  getDetail()
})

// 获取详情数据
const getDetail = () => {
  const params = {
    taskId: tId.value,
    positionNo: curPositionNo.value,
    zhId: userInfo.zhId,
    createdBy: userInfo.id,
    createdByName: userInfo.unitName,
  }
  taskPointDetailByPositionNoAPI(params).then((res) => {
    detailData.value = res.data
    // detailData.value.inspectionResult = [
    //   {
    //     id: '1',
    //     videoResult: 2,
    //     disposeId: '53853c661f023078cfdb691001e94014',
    //     eventTypeName: '测试1-火警',
    //     videoResultName: '异常',
    //     disposeEventType: 1,
    //   },
    //   {
    //     id: '1',
    //     videoResult: 2,
    //     disposeId: 'e56a2aa3d32822201420ef6a9aeab4c0',
    //     eventTypeName: '测试2-隐患',
    //     videoResultName: '异常',
    //     disposeEventType: 4,
    //   },
    //   { id: '2', videoResult: 1, disposeId: '1', eventTypeName: '测试3', videoResultName: '正常' },
    // ]
    // detailData.value.videoUrl = '20250815/7c9ea6ef5f6a481f876d6c7fde5bd7d5.jpg'
  })
}

function handlePrev() {
  if (curPositionNo.value > 1) {
    curPositionNo.value -= 1
  } else {
    curPositionNo.value = 1
  }
  console.log(curPositionNo.value, 'curPositionNo.value')

  getDetail()
}

function handleNext() {
  const total = zongNum.value || 1
  console.log(total, 'total')
  if (curPositionNo.value < total) {
    curPositionNo.value += 1
  } else {
    curPositionNo.value = total
  }

  console.log(curPositionNo.value, 'curPositionNo.value')
  getDetail()
}
// 显示放大的图片
const showZoomedImage = (url: string) => {
  zoomedImageUrl.value = url
  // 阻止页面滚动
  document.body.style.overflow = 'hidden'
}

// 关闭放大的图片
const closeZoomedImage = () => {
  zoomedImageUrl.value = ''
  // 恢复页面滚动
  document.body.style.overflow = ''
}

const toVideo = () => {
  uni.navigateTo({
    url: `/pages/taskDetailLive/index?deviceId=${detailData.value.deviceId}&loactionName=${detailData.value.deviceAddress}`,
  })
}
</script>

<style scoped lang="scss">
.detail {
  width: 100vw;
  .btn {
    display: flex;
    width: 100vw;
    .btn1 {
      position: fixed;
      bottom: 0;
      width: 49.5vw;
      color: white;
      background-color: #0256ff;
      border-radius: 0;
    }
    .btn2 {
      position: fixed;
      right: 0;
      bottom: 0;
      width: 49.5vw;
      color: white;
      background-color: #0256ff;
      border-radius: 0;
    }
  }
  .ponitDetail {
    // padding: 10px;

    .img {
      width: 100%;
      image {
        width: 100%;
        margin-top: 10px;
        // 添加点击反馈
        cursor: pointer;
        border-radius: 6px;
        transition: transform 0.2s;

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .no-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 25vh;
      background-color: #18294a;

      image {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding-top: 60px;
        object-fit: contain;
        border-radius: 6px;
      }
    }
    .card1 {
      height: 190px;
      padding: 10px;
      margin-top: 20px;
      background: #ffffff;
      border-radius: 6px;
      .title {
        display: flex;
        justify-content: space-between;
        .video {
          font-size: 13px;
          font-weight: 400;
          color: #3b87f9;
        }
      }
      .list {
        height: 160px;
        margin-top: 10px;
        overflow: auto;
        .li {
          margin-bottom: 5px;
        }
      }
    }
    .card2 {
      height: 190px;
      padding: 10px;
      margin-top: 20px;
      background: #ffffff;
      border-radius: 6px;
    }
  }

  // 图片放大弹窗样式
  .image-zoom-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.9);
  }

  .zoomed-image {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>
