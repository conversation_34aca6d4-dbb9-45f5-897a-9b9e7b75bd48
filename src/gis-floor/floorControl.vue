<template>
  <view class="control-wrapper">
    <!-- <BsChevronDoubleUp class="arrow" /> -->
    <!-- <n-scrollbar content-class="h-full" :style="{ maxHeight: '200px' }"> -->
    <view class="floor-list">
      <view
        v-for="item in floorList"
        :key="item.floorId"
        :class="['floor-item', curFloorId === item.floorId ? 'floor-item_actived' : '']"
        @click="changeHandle(item.floorId)"
      >
        {{ item.floorName }}
      </view>
    </view>
    <!-- </n-scrollbar> -->
    <!-- <BsChevronDoubleDown class="arrow" /> -->
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { FloorGisService as GisService } from './floorGisService'
// import { BsChevronDoubleUp, BsChevronDoubleDown } from '@kalimahapps/vue-icons'
import { getFloorList } from './featch'
import { EGisType } from './constant'

defineOptions({ name: 'floorControlComp' })

const curFloorId = GisService.curFloorId

const floorList = ref<
  Array<{
    floorId: string
    floorName: string
    [key: string]: any
  }>
>([])

const getFloorListData = () => {
  const unitId = GisService.curUnitInfo.value?.unitId || ''
  getFloorList({
    unitId,
    buildId: GisService.curBuildId.value,
  }).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || []
      floorList.value = _data
      // changeHandle(_data[0]?.floorId)
    }
  })
}

const emits = defineEmits(['change'])
const changeHandle = (floorId: string) => {
  emits('change', floorId)
}

defineExpose({
  getFloorListData,
})
</script>

<style scoped lang="scss">
.control-wrapper {
  @apply py-[10px] flex flex-col gap-[10px] items-center;
}

.arrow {
  font-size: 18px;
  transform: translateX(-5px);
}

.floor-list {
  @apply pr-[0.1rem] flex flex-col gap-[6px];
  height: 40vh;
  overflow: auto;

  .floor-item {
    @apply p-[8px] cursor-pointer;
    color: #fff;
    background-color: rgba(11, 95, 144, 0.2);
    border: 1px solid;
    border-color: rgba(58, 189, 255, 0.4);
    border-radius: 4rpx;

    &.floor-item_actived {
      background-color: rgba(11, 95, 144, 0.75);
      border-color: rgba(58, 189, 255, 0.75);
    }
  }
}
</style>
