// 列表模式-设备
export interface IDeviceRow {
  algoList?: any[]
  buildingId: string
  buildingName?: string
  deviceAddress?: string
  deviceId: string
  deviceName?: string
  deviceNum?: string
  floorId: string
  floorName?: string
  loopDeviceNum?: string
  mapX: number
  mapY: number
  mapZ: number
  onlineState?: string
  videoLatitude: number
  videoLongitude: number
  videoSort: number
  manufacturerCode?: string | number
  latitude: string | number
  longitude: string | number
  [props: string]: any
}

export interface IErecordUnit {
  // 系统单位id
  treeId?: string
  // 电子档案id
  unitId: string
  // 5-列表
  serviceModelCode: number
  // 1-2.5D  13-多楼层
  floorMapType: number
  [key: string]: any
}
