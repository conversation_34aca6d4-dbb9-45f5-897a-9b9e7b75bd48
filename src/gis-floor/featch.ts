import { http } from '@/utils/http'
import { api } from '@/api'

export function getBuildingListByUnitId(unitId: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getBuildingListByUnitId)
  return http.get(url, {
    unitId,
  })
}
export function getFloorList(query: { unitId: string; buildId: string }) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getFloorList)
  return http.get(url, { ...query })
}

// 查询电子档案单位信息
export function getErecordUnitInfo(orgCode: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getErecordUnitInfo, { orgCode })
  return http.post(url)
}
