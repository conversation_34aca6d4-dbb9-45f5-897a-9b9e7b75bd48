import _round from 'lodash-es/round'

function transCoord(curCoord: number, targetCoord: number): (...args: any) => [number, number] {
  return function (...args: any) {
    const [_lon, _lat] = [...args]
    const pPnt = new window.GISShare.SMap.Geometry.Point(+_lon, +_lat)
    window.GISShare.SMap.Fitting.FittingHelper.Fit(pPnt, curCoord, targetCoord)
    return [_round(pPnt.getX(), 6), _round(pPnt.getY(), 6)]
  }
}

const gisFn = {
  /**
   * 地图居中
   * @param data
   * @param ins
   * @param pixel 偏移量
   */
  setCenter(data: [lng: number, lat: number], ins?: any, pixel?: [x: number, y: number]) {
    const indoor = ins || window.gisIns
    indoor.setCenter(data, pixel)
  },

  // 坐标转换
  BDLLToBDMC: transCoord(-1024326, -2023857),
  BDLLToWGS84: transCoord(-1024326, 4326),

  BDMCToBDLL: transCoord(-2023857, -1024326),
  BDMCToWGS84: transCoord(-2023857, 4326),

  WGS84ToBDLL: transCoord(4326, -1024326),
  WGS84ToBDMC: transCoord(4326, -2023857),

  /**
   * 添加mark
   * @param mapIns
   * @param pointData
   * @param config
   */
  addMarkPoi(
    mapIns: any,
    pointData: any,
    config?: {
      style?: any
      styleOption?: {
        src: string
        anchor: [number, number]
        scale: number
      }
      x?: string
      y?: string
    },
  ) {
    let _style
    if (config?.style) {
      _style = config.style
    } else {
      if (config?.styleOption) {
        _style = window.IndoorMap.createImageStyle(config.styleOption)
      }
    }
    const _x = config?.x || 'x'
    const _y = config?.y || 'y'
    const _mark = mapIns.addNewMark(
      pointData, // 绑定数据
      _x, // X轴对应的字段名称,
      _y, // Y轴对应的字段名称
      undefined, // Z轴对应的字段名称（可去省）
      _style, // 样式
    )
    return _mark
  },

  /**
   * 三位地图视图控制
   * @param {SViewFlyToOptionsType} options
   * @param {number=} [options.radius] target为点时，生效
   * @param {number=} [options.interval] 动画时长（大于0有效，时间设置无效）
   * @param {number=} [options.zoom] zoom（层级，当distance不存在且target为点时生效）
   * @param {number=} [options.distance] distance（视距，当target为点时生效）
   * @param {number=} [options.polar] polar（弧度）
   * @param {number=} [options.azimuth] azimuth（弧度）
   * @param {number=} [options.expand] expand
   * @param {Array.<number>=} [options.offset] offset
   * @return {Promise<any>|undefined}
   */
  flyTo(gisIns: any, coords: [number, number], option?: any) {
    let pPoint = new window.GISShare.SMap.Geometry.Point(coords[0], coords[1])
    return gisIns
      .getMap()
      .getView()
      .FlyTo(pPoint, {
        interval: 1000,
        radius: 400,
        ...option,
      })
      .finally(() => {
        pPoint = null
      })
  },
}

export { gisFn }
