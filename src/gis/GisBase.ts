import { IGisConfig } from './types'
import { IObj } from '@/types'
import _ from '@/utils/lodash'

const gisUrl = window.$SYS_CFG.gisDBService

export class GisBase {
  // 单位平面图缓存
  private static CONST_GSCache = {
    adminCodeDicCache: new window.DicCache(50), // 2D & 2.5D & 3DM
    indoorAreaDicCache: new window.DicCache(16), // 2D & 2.5D & 3DM
    indoorAreaExtentDicCache: new window.DicCache(32), // 2D & 2.5D & 3DM
    gridAreaDicCache: new window.DicCache(16), // 2D & 2.5D & 3DM
    ovBuildAreaDicCache: new window.DicCache(16), // 2D & 2.5D & 3DM
    ovUnitModelInfoDicCache: new window.DicCache(2), // 2.5D & 3DM
    floorModelInfoDicCache: new window.DicCache(2), // 2.5D & 3DM
    indoorArea3DMDicCache: new window.DicCache(4), // 3DM
  }

  // 常规线上服务配置
  private static CONST_GSOptions = {
    dbService: window.newIndoorService(gisUrl + '/api/v3/bw-svc-indoor-gis-service/indoorMap'),
    dbService_Record: window.newIndoorService(gisUrl + '/api/v3/bw-svc-indoor-gis-service/record'),
    dbService_GeoSpatial: window.newIndoorService(
      gisUrl + '/api/v3/bw-svc-indoor-gis-service/GeoSpatial',
    ),
    dbService_Analysis: window.newIndoorService(
      gisUrl + '/api/v3/bw-svc-indoor-gis-service/indoorMapAnalysis',
    ),
    unitUrlHeader: gisUrl + '/img1/floorImage',
    deviceIconUrlHeader: gisUrl + '/img1/deviceIcons/_v3.0',
    sky: false,
    deviceIconAlarmGifUrl: gisUrl + '/img1/deviceIcons/gif/alarm.gif',
    skyUrl: [
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/right.jpg',
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/left.jpg',
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/back.jpg',
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/front.jpg',
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/up.jpg',
      gisUrl + '/img1/deviceIcons/z/sky/box_z/7/down.jpg',
    ],
    ovUnitModelUrlHeader: gisUrl + '/img1/indoor',
    wmsURL: gisUrl + '/geoserver/GS/wms',
    deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
    deviceFieldNameOnlineState: undefined,
    deviceStateValueConvertFun: window.CONST_Function_DeviceStateValueConvertFun_Default_3,
    gridLoad: true,
    fullFloorInteraction: true,
    styleInfo: window.CONST_StyleInfo_Default_Deep,
    // 【3.0 使用该配置】
    videoBufferQueryVideoTypeCode: '25030000',
    deviceFieldNameX: 'mapX', // 设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
    deviceFieldNameY: 'mapY', // 设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
    deviceFieldNameZ: 'mapZ',
    deviceFieldNameX0: 'longitude', // 设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
    deviceFieldNameY0: 'latitude', // 设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）
    // 自定义设备图标
    deviceIconUrlFilter: function (deviceIconUrl: string, data: IObj<any>) {
      return data.deviceTypeId === '25030000'
        ? {
            src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAPmSURBVEiJrZZPSJt3GMc/79v4J5ZGyVqqU4ey7LBGTw4xhxEGA9PDDrLGg15asoNlLVQhUE8TLxZk5rCVjaJMaDdY09HDDo1QkOAhTraTZqWYocRYY7VZ8paaavT97fAzrXF54+vmF0L09+f5Ps/ze54nXzCByKLozuVEIL4p4uktsbOnC31PF3p6S+w818TTDU3cmY6KXjO2FKMNIYQaf4H/dDmDSY3q2RiE5iGRAi0rz9is4KwHlwM6HNB0lmRO51aNlW8URdFNE87ERFuznUe64FwgBME5M75L4n4PfPguSwtreD92KH8cSZjeEpcqy7h3+zEVgZA5osPwuWHwM7b/WqfP2aBMGhJGE+Ly+Wp+uDoJkdh/I8vD5YDvLoPlFN6aKuVBfl3N/zETE23vn+f7kyADaePqJFSWcW8mJtoKCIUQaksdwZFfqTAi63DAhfrjk95+TEWznUdCCPUNYTrL9SfPaJ4IF7/ockDwGkz54f41aLCbJw2EQBeci7/A/4awTOWmUYHYrPB1TyH5lF9W43FIT5czCKBOR0Xv8ia1Rqn0uaHxUEQ2Kwx4IOSXDhyF4BwkNaoji6JbbWnEPVuiSJwl3s1ZL1P8VZd0ohRmY/BREy5VgLtUVX4xAePht9Ol6Bm3jLaz1fhMaB7WMnyulltojq4aH+xslQaDc6UnTqMdxn3GRZVIga2KWvVMJZZS3k/Nw9BD8LbL1hgLle7TfFH53IXrWhbOVGJRjS6C7Ltxn/TONSwjHPDIy0MPYSVV/J7NCkNdMNbz7z315Wt2jR7cZpUpvVAvSRIpSdpgl8TBORlxsQzl9w7aevmaXYu2RbLBTmPCwFv2q9HbLtPkrJfR/bkq+zORkv93tsrPSgq6v5Xrh23s7LKk1lXzi6dEdbFfOGM98lJwDibC8h09o/J7qEsSeUblXrGIXQ5QIKz+vkyk44jmnQjLN3QNw8BPb9e1rJwirmGotsrWsFmLE3Y4YGGFsOr6QLlfayPjbTcmzOy/n1Hatax0pPGGdKBYdE1nSX7iVH5UAV7tMGI0GyMxOSX+D/o9kNO5Rf4HWAihrv5N8uffpKQ4SfjccKOTpZoqHIqi6CqAoij6UoqLX37KtplhbBYuh5QaC2t486KqQGKkt8Sl3T2CJykx1jNcOahrCiZNTZXyYD3Dlbt9bB8eTcdBvwfu9rFtOYW3pIjKYyYm2lrqCD55RnPgiNl5EN52SaYqbCyluGhKJuYhhFDTWa6Xqdxc3qR2NiaJo6uFQrjBDp5W2We1NjKvdhh57x1GjYSwKUxHRe+GJu4818TTw1I/viniuZwIRBZFtxlb/wBpR66iTFy7GQAAAABJRU5ErkJggg==',
            size: [28, 28],
            anchor: [0.5, 0.5],
          }
        : deviceIconUrl
    },
  }

  // 获取线上地图样式
  private static CONST_GSParams = {}

  private static defaultConf = {
    center: [13055357.52999, 3710320.81],
    tile: false, // 底图是否可见
    gridLoadViewTypes: window.IndoorMap.ViewType.toViewTypeArray(), // 打开网格
    gridTypeIds: window.IndoorMap.GridAreaType.GridAreaLayerGGQY,
    grid: true,
    gridLoad: true,
    animation: false,
  }

  constructor(config: Partial<IGisConfig>) {
    if (!config.target) {
      throw new Error('初始化地图错误，请指定地图挂载对象')
    }

    const conf = _.merge(GisBase.defaultConf, config)

    return new IndoorThree(
      window.IndoorMap.Merge(
        [GisBase.CONST_GSCache, GisBase.CONST_GSOptions, GisBase.CONST_GSParams, conf],
        {},
      ),
    )
  }

  static async create(targetEl: string, ins = window.gisIns) {
    if (ins) {
      const oldDom = ins.getElement()
      const oldEl = document.querySelector('#' + targetEl)
      if (oldEl) {
        oldEl.replaceWith(oldDom)
      }
      return Promise.resolve(ins)
    }
    return new Promise((resolve) => {
      const _ins = new GisBase({
        target: targetEl,
        onLoad: (ins: any) => {
          resolve(ins)
        },
      })
    })
  }
}
