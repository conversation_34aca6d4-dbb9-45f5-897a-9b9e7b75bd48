<template>
  <wd-config-provider :themeVars="themeVars">
    <view class="box-border" :style="{ paddingTop, height }">
      <slot />
    </view>
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'

const themeVars: ConfigProviderThemeVars = {}
const paddingTop = ref('')
const height = ref('')
const remoteLoadComplete = ref(false)

function computedHeight() {
  paddingTop.value = document.querySelector('.navbar')?.clientHeight + 'px'
  // debugger
  const screenHeight = window.innerHeight
  height.value = `${screenHeight}px`
}
onMounted(() => {
  computedHeight()
})
</script>
