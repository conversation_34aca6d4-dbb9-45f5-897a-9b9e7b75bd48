<template>
  <view class="preview-overlay" @touchmove.stop.prevent="handleTouchMove">
    <!-- 顶部关闭按钮 -->
    <view class="close-btn" @click="closePreview">
      <wd-icon name="close" color="#fff" size="24"></wd-icon>
    </view>

    <!-- 图片展示区域 -->
    <swiper class="preview-swiper" :current="currentIndex" @change="onSwiperChange">
      <swiper-item v-for="(item, index) in imageList" :key="index">
        <image :src="item" mode="aspectFit" class="preview-image" @tap="handleImageTap" />
      </swiper-item>
    </swiper>

    <!-- 页码指示器 -->
    <view class="indicator">{{ currentIndex + 1 }}/{{ imageList.length }}</view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps({
  imageList: {
    type: Array as () => string[],
    default: () => [],
  },
  initialIndex: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['close'])

const currentIndex = ref(props.initialIndex)

// 处理滑动切换
const onSwiperChange = (e: any) => {
  currentIndex.value = e.detail.current
}

// 关闭预览
const closePreview = () => {
  emit('close')
}

// 阻止触摸移动事件冒泡（禁止页面滚动）
const handleTouchMove = () => {
  // 空函数，仅用于阻止默认行为
}

// 点击图片切换显示状态
const handleImageTap = () => {
  // 这里可以添加点击图片的交互逻辑
}
</script>

<style lang="scss" scoped>
.preview-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  background-color: #000;

  .close-btn {
    position: absolute;
    top: 60px;
    right: 20px;
    z-index: 1001;
    padding: 10px;
  }

  .preview-swiper {
    flex: 1;
    width: 100%;

    .preview-image {
      width: 100%;
      height: 100%;
    }
  }

  .indicator {
    position: absolute;
    right: 0;
    bottom: 30px;
    left: 0;
    z-index: 1001;
    font-size: 16px;
    color: #fff;
    text-align: center;
  }
}
</style>
