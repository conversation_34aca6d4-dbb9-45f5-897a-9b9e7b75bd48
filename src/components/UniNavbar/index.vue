<template>
  <div
    class="UniNavbar navbar"
    :class="{ bgc: !props.transparent }"
    v-if="showNavBar"
    :style="{ paddingTop: appInfo.top + 'px' }"
  >
    <div class="nav-bar-content">
      <div class="left" @click="handleClickLeft">
        <div class="icon-back" v-if="leftArrow"></div>
      </div>
      <div class="title">{{ title }}</div>
      <div class="right">
        <slot name="right"></slot>
      </div>
    </div>
    <div class="bottom">
      <slot name="bottom"></slot>
    </div>
  </div>
  <div class="miniprogram-navbar navbar" :class="{ bgc: !props.transparent }" v-else>
    <div class="miniprogram-right">
      <slot name="right"></slot>
    </div>
    <div class="bottom">
      <slot name="bottom"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
declare const uni: any
// declare const getCurrentPages: any;

// 控制右边btn显示
const props = withDefaults(
  defineProps<{
    setNavigationBarTitle: (data: any) => void
    leftArrow?: boolean
    rightBtn?: boolean
    searchShow?: boolean
    transparent?: boolean
    title: string
    appEnv: {
      plus?: boolean
      miniprogram?: boolean
      osAndroid?: boolean
      osIos?: boolean
      wychApp?: boolean
    }
    appInfo: {
      top?: number
      bottom?: number
      left?: number
      right?: number
      width?: number
      height?: number
    }
  }>(),
  {
    leftArrow: true,
    rightBtn: false,
    searchShow: false,
    transparent: false,
    title: '',
    appEnv: () => ({
      miniprogram: false,
      plus: false,
    }),
    appInfo: () => ({ top: 0 }),
  },
)
const showNavBar = !props.appEnv.miniprogram
const isH5 = !props.appEnv.miniprogram && !props.appEnv.plus
watch(
  () => props.title,
  (newVal) => {
    if (newVal) {
      props.setNavigationBarTitle?.({
        title: newVal,
      })
    }
  },
  { immediate: true },
)

function handleClickLeft() {
  console.log('点击返回')
  console.log('props.appEnv.miniprogram', props.appEnv.miniprogram)
  console.log('props.appEnv.plus', props.appEnv.plus)
  if (isH5) {
    history.back()
    return
  }
  console.log('uni', uni)
  if (history.length <= 1 || props.title === '智能任务巡检') {
    uni.webView.navigateBack()
  } else {
    uni.navigateBack()
  }
}
</script>
<style lang="scss" scoped>
.icon-back {
  width: 40px;
  height: 40px;
  background-image: url('./assets/back.png');
  background-size: 100% 100%;
}

.bgc {
  color: #fff;
  background-color: #2169fd;

  .icon-back {
    background-image: url('./assets/back-white.png');
  }
}

.left,
.right {
  width: 50%;
}
.right {
  box-sizing: border-box;
  padding-right: 10px;
  text-align: right;
}

.title {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 18px;
  font-weight: 600;
  transform: translate(-50%, -50%);
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: content-box;
  width: 100%;
}
.nav-bar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 44px;
}
.bottom {
  width: 100%;
}
.miniprogram-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
