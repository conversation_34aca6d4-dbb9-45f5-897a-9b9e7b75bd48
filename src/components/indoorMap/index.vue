<template>
  <div class="w-full h-full relative">
    <!-- <div class="h-[15px] absolute top-[0] z-20" @click="loadFloorData">6666666666</div> -->
    <div
      class="w-full relative"
      :class="height"
      ref="ElGIs"
      :style="{ opacity: floorData?.floorId && loadFinished ? 1 : 0 }"
    ></div>
    <wd-status-tip
      class="absolute top-20% left-0"
      :image="emptyImg"
      tip="暂无点位信息，无法展示"
      v-if="!floorData?.floorId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { GISOBJ } from './gisGlobal'
import emptyImg from '@/static/images/location-empty.png'
import fireImgPop from './fireImgPop.vue'
import { getAllVideoList, currRoute } from '@/utils/index'

const ElGIs = ref()
interface DeviceItem {
  deviceId: string
  deviceTypeId: string
  eventType?: string
  priorityEventType?: string
  mapX: number
  mapY: number
  mapZ?: number
  longitude: number
  latitude: number
  [key: string]: string | number
}
interface Props {
  deviceList?: DeviceItem[]
  floorInfo?: {
    unitId: string
    buildingId: string
    floorId: string
    floorAreaImg?: string
  }
  pointer?: {
    x: number
    y: number
  }
  isAddMark?: boolean
  height?: string
  isFireImg?: boolean
  isShowAxld?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  deviceList: () => [],
  isAddMark: false,
  height: 'h-700px',
})
const isSuccess = ref(true)
const isLoading = ref(false)
const emits = defineEmits(['addMark'])
const floorData = computed(() => {
  return props?.floorInfo
})
const pointer = computed(() => {
  return props?.deviceList || []
})
const defPonter = computed(() => {
  return props?.pointer || {}
})
const loadFinished = ref(false)

watch(
  () => props.isFireImg,
  (val) => {
    if (val) {
      addFireImg()
    } else {
      GISOBJ.clearPopup()
    }
  },
  {
    deep: true,
  },
)

watch(
  () => floorData.value,
  (val) => {
    if (val?.unitId && val.buildingId && val.floorId) {
      loadFloorData()
    }
  },
  {
    deep: true,
  },
)

watch(
  () => pointer.value,
  () => {
    loadFloorData()
  },
  {
    deep: true,
  },
)
watch(
  () => defPonter.value,
  () => {
    if (props.isAddMark) {
      addDeftPointer()
    }
  },
  {
    deep: true,
  },
)
const axldLists = ref<any>([])
const addDeftPointer = () => {
  gisMap3DM.addNewMark({ ...defPonter.value, z: 0 })
}
let popup: any

let gisMap3DM: any
const initGis = () => {
  ElGIs.value.appendChild(GISOBJ.getDom())
  gisMap3DM = GISOBJ.getIndoorMap()
  ;(window as any).gisMap3DM = gisMap3DM
  GISOBJ.render()
  gisMap3DM.clearPopup()
}

const loadFloorData = async () => {
  // 渲染平面图
  const _pointer = [...pointer.value]
  console.log('🚀 ~ loadFloorData ~ _pointer:-11111', _pointer)
  try {
    if (props.isShowAxld) {
      axldLists.value = await getAllVideoList(floorData.value, 25)
      let _allList = [...axldLists.value].map((i) => {
        return {
          ...i,
          mapX: i.mapx,
          mapY: i.mapy,
          mapZ: i.mapz || 0,
          deviceTypeId: i.devicetypeid,
        }
      })
      const videoTypes = '25030000'
      // '23130000,23160000,24360000,,25060000,23030000,23080000,23100000,23110000,25050000,23270000,25080000'
      _pointer.forEach((i) => {
        if (videoTypes.includes(i.deviceTypeId)) {
          _allList = _allList.filter((item) => i.deviceId !== item.deviceid)
          i.deviceTypeId = '02040000'
        }
      })
      _pointer.push(..._allList)
    }
  } catch (error) {
    console.log('🚀 ~ loadFloorData ~ error:', error)
  }

  gisMap3DM.showFloorData(
    window.IndoorMap.ViewType.IndoorAreaVector,
    floorData.value?.unitId ?? undefined, // 单位id
    floorData.value?.buildingId ?? undefined, // 楼栋id
    floorData.value?.floorId, // 楼层id
    // undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
    floorData.value?.floorAreaImg
      ? GISOBJ.baseurl + '/img1/floorImage/' + floorData.value.floorAreaImg
      : undefined,
    function (mapType: string, success: any, objArgs: any, indoor: any) {
      isSuccess.value = success
      // isLoading.value = true
      loadFinished.value = true

      GISOBJ.render()
      if (props.isAddMark) {
        addDeftPointer()
      }
      indoor.showFloorDataDevice(_pointer ?? [], 'mapX', 'mapY', 'mapZ', function filter(data) {
        console.log('🚀 ~ filter ~ data:', data)
        // 过滤条件，可对数据做修改，返回true表示去掉此条数据
        // item, markFieldNameX, markFieldNameY
        return false
      })

      if (pointer.value.length) {
        objArgs.cancelZoomToExtent = true
        indoor.zoomToExtent([pointer.value[0].mapX, pointer.value[0].mapY, 0])
      }
      if (props.isFireImg) {
        addFireImg()
      }
    },
  )
}
const bandEvent = () => {
  gisMap3DM.onMouseClick = function (e: any) {
    if (!props.isAddMark) return
    gisMap3DM.clearNewMark()
    GISOBJ.render()
    const pointer = gisMap3DM
      .getMap()
      .getView()
      .ClientToScene(new window.THREE.Vector2(e.getX(), e.getY()), 0)
    const { x, y } = pointer
    gisMap3DM.addNewMark({ x, y, z: 0 })
    emits('addMark', { x, y })
  }
  // 楼层图斑点击事件
  gisMap3DM.onAreaSelected = function (data: any, e: any, obj: any, target: any) {
    console.log('🚀 ~ bandEvent ~ 楼层图斑点击事件.point:', target.point)
  }
  // 楼层网格点击事件
  gisMap3DM.onGridSelected = function (data: any, e: any, obj: any, target: any) {}
  gisMap3DM.onNullSelected = function (data: any) {}
  // 设备点位图标 点击事件
  gisMap3DM.onDeviceSelected = function (data: any, e: any, obj: any, target: any) {}
}
const imgPop = GISOBJ.getPopup()
const addFireImg = () => {
  GISOBJ.clearPopup()
  const data: any = floorData.value
  const abc = GISOBJ.showFireImgPop(data, {
    coms: fireImgPop,
    fireImage: data.fireImage,
  })

  console.log('🚀 ~ addFireImg ~ floorData:', abc)
}

onMounted(() => {
  initGis()
  bandEvent()

  setTimeout(() => {
    loadFloorData()
    // addFireImg()
  }, 500)
})

defineOptions({ name: 'floorGis' })
</script>

<style module lang="scss"></style>
