<template>
  <div class="h-full w-full">
    <div class="h-full w-full">
      <div class="rounded h-full w-full">
        <div class="w-full h-[600px] relative">
          <floorMap
            :floor-info="floorData"
            :device-list="deviceList"
            :isAddMark="true"
            :pointer="pointer"
            @add-mark="addMark"
          ></floorMap>
        </div>
      </div>
    </div>
  </div>
</template>
<!--
  //在index.html 中引入 gis css js
    <link
      rel="stylesheet"
      href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree_CSS&wgId=67"
    />
     -->
<!--
  <script
    type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=IndoorThree&wgId=67"
  ></script>
-->

<script setup lang="ts">
import { onMounted, ref, h, watch } from 'vue'
import floorMap from './index.vue'
import { pointerList } from './data'

const deviceList = ref(pointerList.data)
const pointer = {
  x: 3028718.761922908,
  y: 909491.4322155592,
}
const floorData = ref({
  unitId: '888',
  buildingId: '610000DW1823561724595273728_001',
  floorId: '610000DW1823561724595273728_001_U001',
  floorAreaImg: '/install/610000DW1823561724595273728/001/U001.jpg',
})

const data2 = {
  unitId: 'AHHF_QHHFY_20180408',
  buildingId: 'AHHF_QHHFY_20180408_002',
  floorId: 'AHHF_QHHFY_20180408_002_U001',
  floorAreaImg: '',
}

const data3 = {
  unitId: 'AHHF_QHHFY_20180408',
  buildingId: 'AHHF_QHHFY_20180408_006',
  floorId: 'AHHF_QHHFY_20180408_006_U001',
  floorAreaImg: '',
}

const addMark = (val: { x: number; y: number; text: string }) => {
  console.log('🚀 ~ addMark ~ val:', val)
}

onMounted(async () => {})

defineOptions({ name: 'plannedManagementDetails' })
</script>
<style module lang="scss"></style>
