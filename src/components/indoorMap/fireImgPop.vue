<template>
  <view class="relative h-15px">
    <view class="w-150px h-100px absolute right-0 bottom-10px" v-if="imgUrl">
      <wd-img :width="150" :height="100" :src="imgUrl" :enable-preview="true" />
    </view>
  </view>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { viteBasePrefix } from '@/utils'
const props = defineProps<{
  fireImage: string
}>()

const imgUrl = computed(() => {
  return props.fireImage ? viteBasePrefix() + props.fireImage : ''
})
</script>
