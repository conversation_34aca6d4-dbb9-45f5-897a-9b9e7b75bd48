<template>
  <!-- <component
    :is="UniNavbar"
    v-bind="$attrs"
    :appInfo="appInfo"
    :appEnv="appEnv"
    :setNavigationBarTitle="setNavigationBarTitle"
  >
    <template #right>
      <view @click="handleRightClick" class="items-center color-white">
        <view v-if="rightBtn" class="navbar-batch-btn">全部正常</view>
      </view>
    </template>
  </component> -->
  <UniNavbar
    v-bind="$attrs"
    :appInfo="appInfo"
    :appEnv="appEnv"
    :setNavigationBarTitle="setNavigationBarTitle"
  >
    <!-- <template #right>
      <view @click="handleRightClick" class="items-center color-white">
        <view v-if="rightBtn" class="navbar-batch-btn">全部正常</view>
      </view>
    </template> -->
  </UniNavbar>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store'
// import { UniNavbar } from '@/utils/hel-micro'
import UniNavbar from './UniNavbar/index.vue'
const { appInfo, appEnv } = useAppStore()
const setNavigationBarTitle = uni.setNavigationBarTitle
// 控制右边btn显示
// const props = withDefaults(defineProps<{ rightBtn?: boolean }>(), {
//   rightBtn: false,
// })
// const $emit = defineEmits(['clickRight'])
// function handleRightClick() {
//   $emit('clickRight')
// }
</script>

<style lang="scss" scoped>
.navbar-batch-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  margin-right: 20rpx;
  font-size: 12px;
  color: #0256ff;
  white-space: nowrap;
  background-color: #e5eeff;
  border: 1px solid #0256ff;
  border-radius: 16px;
}
.navbar-batch-btn:active {
  opacity: 0.8;
}
</style>
