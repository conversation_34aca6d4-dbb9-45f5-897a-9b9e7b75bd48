import { pages, subPackages, tabBar } from '@/pages.json'
import { isMp } from './platform'

const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包回报错，所以改用下面这个【虽然我加了src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/** 判断当前页面是否是tabbar页  */
export const getIsTabbar = () => {
  if (!tabBar) {
    return false
  }
  if (!tabBar.list.length) {
    // 通常有tabBar的话，list不能有空，且至少有2个元素，这里其实不用处理
    return false
  }
  const lastPage = getLastPage()
  const currPath = lastPage.route
  return !!tabBar.list.find((e) => e.pagePath === currPath)
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 ‘/pages/login/index’
 * redirectPath 如 ‘/pages/demo/base/route-interceptor’
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 这里设计得通用一点，可以传递key作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)

/**
 * 根据微信小程序当前环境，判断应该获取的BaseUrl
 */
export const getEvnBaseUrl = () => {
  // 请求基准地址
  const baseUrl = import.meta.env.VITE_SERVER_BASEURL

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的UPLOAD_BASEURL
 */
export const getEvnBaseUploadUrl = () => {
  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  // 小程序端环境区分
  if (isMp) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = 'https://ukw0y1.laf.run/upload'
        break
      case 'trial':
        baseUploadUrl = 'https://ukw0y1.laf.run/upload'
        break
      case 'release':
        baseUploadUrl = 'https://ukw0y1.laf.run/upload'
        break
    }
  }

  return baseUploadUrl
}

export function viteBasePrefix() {
  return location.origin + import.meta.env.VITE_BASE_PREFIX
  // return 'https://psmp-sit.sinotrans-csc.com:9833'
}

let axldDb

export const getAllVideoList = (eventInfo, dis = 25) => {
  if (!axldDb) {
    axldDb = window.newIndoorService(
      import.meta.env.VITE_BASE_PREFIX + '/api/v3/bw-svc-indoor-gis-service/indoorMapAnalysis',
    )
  }

  const location = [eventInfo.longitude || eventInfo.mapX, eventInfo.latitude || eventInfo.mapY]
  // 视频设备类型
  const deviceTypeIds = '********'
  // '********,********,********,,********,********,********,********,********,********,********,25080000'
  if (!location[0] || !location[1]) return []
  // floor_id,device_address
  const buildingName = eventInfo.buildingName || 'floorName'
  const floorName = eventInfo.floorName || 'floorName'
  const deviceFieldNames = 'device_address'
  return new Promise<void>((resolve, reject) => {
    window.IndoorMap.analysisDeviceVisualizationEx(
      // window.CONST_GSOptions.dbService_Analysis || '',
      axldDb,
      undefined,
      eventInfo.floorId,
      location[0],
      location[1],
      deviceTypeIds,
      deviceFieldNames,
      async (res) => {
        const result = res.getResult()
        const _list = result
          // ?.filter((i) => i.dis <= dis && i.lx < 1)
          .map((i) => {
            return {
              ...i,
              deviceId: i.deviceid,
              mapX: i.mapx,
              mapY: i.mapY,
              mapZ: i.mapz || 0,
              buildingName,
              floorName,
              deviceAddress: i.device_address,
            }
          })
        _list.sort((a, b) => {
          return a.dis - b.dis
        })
        return resolve(_list)
      },
    )
  })
}

const imageExtension = /\.(jpeg|jpg|png|gif|bmp|tiff|webp)$/i
export function replaceWithThumbnailPath(originalImagePath: string, thumbnailSuffix: string) {
  const originalExtension = originalImagePath.match(imageExtension)
  if (!originalExtension) {
    throw new Error('原图路径没有有效的图片扩展名')
  }
  if (
    originalImagePath.includes('/img1') ||
    originalImagePath.startsWith('/img1') ||
    originalImagePath.startsWith('img1')
  ) {
    originalImagePath = import.meta.env.VITE_HOST + originalImagePath
  } else {
    originalImagePath = import.meta.env.VITE_HOST + '/ehs/' + originalImagePath
  }
  // 如果缩略图后缀包含文件扩展名（例如 "_80X160.jpg"），则直接使用它替换原图的扩展名
  // 否则，将缩略图后缀添加到原图的扩展名之前
  if (thumbnailSuffix.includes('.')) {
    // 缩略图后缀包含点，表示它有自己的扩展名，直接替换原图的扩展名
    return originalImagePath.replace(imageExtension, thumbnailSuffix)
  } else {
    // 缩略图后缀不包含点，表示它是一个参数或简单的后缀，添加到原图的扩展名之前
    return originalImagePath.replace(imageExtension, `${thumbnailSuffix}${originalExtension[0]}`)
  }
}
