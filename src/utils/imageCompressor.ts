import ImageCompressor from 'image-compressor.js'
/**
 * 智能压缩图片（根据指定比例，遍历固定quality区间）
 * @param file 图片文件
 * @param ratio 目标比例（0-1之间），比如0.75表示压成75%的大小
 * @returns Promise<File> 压缩后的文件
 */
export async function smartCompressImage(file: File, ratio: number = 0.75): Promise<File> {
  const reader = new FileReader()

  const loadImage = (file: File): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      reader.onload = (event) => {
        const img = new Image()
        img.onload = () => resolve(img)
        img.onerror = (err) => reject(err)
        img.src = event.target?.result as string
      }
      reader.onerror = (err) => reject(err)
      reader.readAsDataURL(file)
    })
  }

  const compress = (img: HTMLImageElement, quality: number): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      canvas.width = img.width
      canvas.height = img.height

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('无法创建Canvas上下文'))
        return
      }
      ctx.drawImage(img, 0, 0)

      canvas.toBlob(
        (blob) => {
          if (blob) resolve(blob)
          else reject(new Error('无法创建Blob对象'))
        },
        'image/jpeg',
        quality,
      )
    })
  }

  const img = await loadImage(file)

  const originalSizeKB = file.size / 1024
  const targetSizeKB = originalSizeKB * ratio

  const qualityList = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.15, 0.1]

  let bestBlob: Blob | null = null
  let bestDiff = Infinity

  for (let i = 0; i < qualityList.length; i++) {
    const quality = qualityList[i]
    const blob = await compress(img, quality)
    const sizeKB = blob.size / 1024

    const diff = Math.abs(sizeKB - targetSizeKB)

    if (diff < bestDiff) {
      bestBlob = blob
      bestDiff = diff
    }
  }

  // 就算没有完全符合的，选最接近的 bestBlob
  if (!bestBlob) {
    // throw new Error('压缩失败（没有找到任何压缩结果）')
  }

  const compressedFile = new File([bestBlob], file.name, {
    type: 'image/jpeg',
    lastModified: Date.now(),
  })

  console.log('原始文件大小:', originalSizeKB.toFixed(2), 'KB')
  console.log('最终压缩大小:', compressedFile.size / 1024, 'KB')

  return compressedFile
}
/**
 * 根据文件大小自动选择压缩质量
 * @param file 图片文件
 * @returns Promise<File> 压缩后的文件
 */
export function CanvasCompressImage(file: File): Promise<File> {
  const fileSizeMB = file.size / (1024 * 1024)

  if (fileSizeMB > 10) {
    return Promise.reject(new Error(''))
  }

  if (fileSizeMB <= 2) {
    return Promise.resolve(file)
  } else if (fileSizeMB <= 5) {
    return smartCompressImage(file, 0.75) // 压到75%
  } else if (fileSizeMB <= 7) {
    return smartCompressImage(file, 0.45) // 压到45%
  } else {
    return smartCompressImage(file, 0.3) // 压到30%
  }
}

/**
 * 图片压缩工具
 * 使用ImageCompressor.js插件实现图片压缩
 */
export function autoCompressImage(file: File): Promise<File> {
  const fileSizeMB = file.size / (1024 * 1024)

  // 设置压缩选项
  const options: any = {
    maxWidth: 1200,
    maxHeight: 1200,
    format: 'jpg',
    lossless: false,
  }

  // 0-2MB不压缩
  if (fileSizeMB <= 2) {
    console.log('文件小于2MB，不进行压缩')
    return Promise.resolve(file)
  }
  // 2-5MB压缩到75%
  else if (fileSizeMB <= 5) {
    options.quality = 0.75
  }
  // 5-7MB压缩到45%
  else if (fileSizeMB <= 7) {
    options.quality = 0.45
  }
  // 7-10MB压缩到10%
  else {
    options.quality = 0.3
  }

  // 使用ImageCompressor进行压缩
  return new Promise((resolve, reject) => {
    const compressionOptions: any = {
      ...options,
      success(result: any) {
        // 将压缩后的 Blob 转换为 File 对象
        const compressedFile = new File([result], file.name, {
          type: file.type,
          lastModified: Date.now(),
        })

        console.log('原始文件大小:', file.size / 1024, 'KB')
        console.log('压缩后文件大小:', compressedFile.size / 1024, 'KB')

        resolve(compressedFile)
      },
      error(e: any) {
        reject(e)
      },
    }

    // 执行压缩
    // eslint-disable-next-line no-new
    new ImageCompressor(file, compressionOptions)
  })
}
