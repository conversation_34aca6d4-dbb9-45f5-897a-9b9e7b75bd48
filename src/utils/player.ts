import { stringify } from 'querystringify'

// 定义：视频流厂商品牌对应的流协议
const protocolMap: Record<string, string> = {
  '': 'm3u8', // 品牌为空默认m3u8
  hikVersion: 'ws-flv',
  dahua: 'rtsp',
  ezviz: 'ezopen', // 萤石云
}

/**
 * 视频地址拼接
 * @param url 视频地址
 * @param options 其他参数
 * @param protocol
 * @param playerType 可选参数，指定播放器，优先级高于protocol
 * @param brandType hikVersion| dahua | ezviz
 */
export function getVideoPlayerUrl(
  url: string,
  protocol = '',
  options: Record<string, any> = {},
  playerType?: string,
  brandType?: string,
) {
  const option = {
    url,
    protocol,
    options: JSON.stringify(options),
  }

  if (playerType) {
    options.playerType = playerType
  }

  if (brandType) {
    options.protocol = protocolMap[brandType] || protocol
  }

  return import.meta.env.VITE_VIDO_PLAYER_URL + '?' + stringify(option)
}
