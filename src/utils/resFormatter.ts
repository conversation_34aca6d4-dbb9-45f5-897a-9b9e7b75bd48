const LABELS = {
  ICHNOGRAPHY_NOT_COLLECTED: '单位未采集图纸信息，无法展示',

  NO_POINT: '暂无点位信息，无法展示',

  NOOP: '无',

  NO_GATHER_DEVICE: '未采集设备',

  NO_GATHER: '未采集',

  NO_ENTERING: '未录入',

  ZERO: '无',

  TO_BE_GENERATED: '待生成',
}

export function normalizeAddress(item) {
  let address =
    (item.buildingName || '') +
    '' +
    (item.floorName || '') +
    '' +
    (item.deviceAddress || item.faultAddress || '')
  if (item._isNine) {
    address = (item.houseNumber || '') + (item.deviceAddress || '')
  }
  return address === '' ? LABELS.NO_GATHER : address
}
