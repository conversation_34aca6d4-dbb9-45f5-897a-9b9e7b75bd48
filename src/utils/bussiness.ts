import { http } from './http'
import { useUserStore } from '@/store'
const { userInfo } = useUserStore()
export const EVENT_TYPE = {
  // 火警
  ALARM: '1',
  // 预警
  WARNING: '2',
  // 故障
  FAULT: '3',
  // 隐患
  HIDDEN_DANGER: '4',
  // 动作
  ACTION: '5',
  // 离线
  OFFLINE: '7',
  // 疑似真警
  MAYBE_ALARM: '8',
  // 屏蔽
  SHIELD: '9',
  // 通知通告
  PULIC_NOTICE: '10',
  // 个人待办
  PERSONAL_PENDING: '11',
  // 消控室任务通知
  ROOM_MESSAGE: '12',
  // 催促
  URGE: '101',
  // 真警
  REAL_ALARM: '102',
  // 安全风险提醒
  SAFETY_RISK: '103',
  // 设备封停
  SINGLE_SEAL: '104',
  // 系统通知
  SYSTEM: '105',
  // 隐患超期
  HIDDEN_DANGER_EXCEED: '106',
  // 离岗
  LEAVE_POST: '107',
  // 服务到期
  SERVICE_EXCEED: '108',
  // 隐患整改意见
  HIDDEN_DANGER_REFORM_SUGGESTION: '109',
  // 故障超期
  FAULT_EXCEED: '113',
  // 数据决策
  DATE_DECISION: '13',
  // 停用账号、删除账号
  STEP_MESSAGE: '114',
  // 动火
  HOTWORK: '120',
  // 高处作业
  HIGHWORK: '124',
  // 计划管理
  PLANNED: '115',
  // 满意度
  SATISFACTION: '125',
}
export const EVENT_LABEL = {
  [EVENT_TYPE.ALARM]: '火警',
  [EVENT_TYPE.WARNING]: '预警',
  [EVENT_TYPE.FAULT]: '故障',
  [EVENT_TYPE.HIDDEN_DANGER]: '隐患',
  [EVENT_TYPE.ACTION]: '动作',
  [EVENT_TYPE.OFFLINE]: '离线',
  [EVENT_TYPE.MAYBE_ALARM]: '高度疑似真警',
}
export const TYPES = {
  ADMIN_GROUP_NAME: 'ADMIN_GROUP_NAME',

  COMMON_GROUP_NAME: 'COMMON_GROUP_NAME',

  REFRESH_ACTION: 'REFRESH_ACTION',

  REVISE_USER_INFO: 'REVISE_USER_INFO',

  REFRESH_FAULT: 'REFRESH_FAULT',

  REFRESH_WARNING: 'REFRESH_WARNING',

  REFRESH_ALARM: 'REFRESH_ALARM',

  REFRESH_HIDDEN_DANGER: 'REFRESH_HIDDEN_DANGER',

  REFRESH_MESSAGE: 'REFRESH_MESSAGE',

  REFRESH_DATA: 'REFRESH_DATA',

  USER_INFO: 'USER_INFO',

  MESSAGE_COUNT: 'MESSAGE_COUNT',

  RECEIVE_PUSH: 'RECEIVE_PUSH',

  SELECT_DEVICE: 'SELECT_DEVICE',

  SELECT_BUILD_FLOOR: 'SELECT_BUILD_FLOOR',

  INSPECT_CONVERT_HIDDEN: 'INSPECT_CONVERT_HIDDEN',

  RESRESH_SCORCE: 'RESRESH_SCORCE',

  PLAY_AUDIO: 'PLAY_AUDIO',

  AUDIO_PAUSE: 'AUDIO_PAUSE',

  REFRESH_MESSAGE_LIST: 'REFRESH_MESSAGE_LIST',

  GS_styleInfo_2D: 'GS_styleInfo_2D',
  GS_styleInfoGrid_2D: 'GS_styleInfoGrid_2D',
  GS_styleInfo_3D: 'GS_styleInfo_3D',
  GS_styleInfoGrid_3D: 'GS_styleInfoGrid_3D',
  GS_tileURL: 'GS_tileURL',
  GS_tileURL_Satellite: 'GS_tileURL_Satellite',
  GS_tileURL_Traffic: 'GS_tileURL_Traffic',
  GS_tileURL_Annotation: 'GS_tileURL_Annotation',
  GS_FireDeviceSystemType: 'GS_FireDeviceSystemType',
  GS_BuildingList: 'GS_BuildingList',
}

export const LABELS = {
  ICHNOGRAPHY_NOT_COLLECTED: '单位未采集图纸信息，无法展示',

  NO_POINT: '暂无点位信息，无法展示',

  NOOP: '无',

  NO_GATHER_DEVICE: '未采集设备',

  NO_GATHER: '未采集',

  NO_ENTERING: '未录入',

  ZERO: '无',

  TO_BE_GENERATED: '待生成',
}
export const BUSINESS = {
  getDeviceFieldList: function (data) {
    console.log(JSON.stringify(data))
    console.log(JSON.stringify(1333))
    if (!data) {
      data = {
        produceInfo: {},
        installInfo: {},
      }
    }
    const deviceClass = data.deviceClassification
    const address = this.normalizeAddress(data)
    const loop = this.normalizeLoop(data)
    const deviceId = data.deviceId || LABELS.ZERO
    const deviceNum = data.deviceNum || LABELS.ZERO
    let temp = []
    if (typeof data.produceInfo === 'string') {
      data.produceInfo = JSON.parse(data.produceInfo || '{}')
    }

    if (typeof data.installInfo === 'string') {
      data.installInfo = JSON.parse(data.installInfo || '{}')
    }
    data.produceInfo = data.produceInfo || {}
    data.installInfo = data.installInfo || {}
    // 1、实时监测-火警、故障、动作  2、事件处置：火警
    console.log(deviceId)
    console.log(8889)
    if (
      (deviceId === LABELS.ZERO || data.deviceTypePid === '') &&
      (data.eventType === '1' || data.eventType === '3' || data.eventType === '5')
    ) {
      temp = [
        { label: '设备编号', value: deviceId },
        { label: '用传/网关编码', value: data.deviceNum || '--' },
        { label: '通道号', value: data.channelNum || '--' },
        { label: '主机回路点位', value: loop || '--' },
        { label: '二次码', value: data.twoCode || '--' },
      ]
    } else if (+deviceClass === 1 || +deviceClass === 2) {
      temp = [
        { label: '设备编号', value: deviceId },
        { label: '主机回路点位', value: loop },
        {
          label: '二次码',
          value: +deviceClass === 1 ? data.twoCode || '--' : '',
        },
      ]
    } else if (['23000000', '24000000'].indexOf(data.deviceTypePid) !== -1) {
      temp = [{ label: 'IMEI', value: deviceNum }]
    } else {
      temp = [{ label: '设备编号', value: deviceId }]
    }

    const base = [
      {
        label: '系统类型',
        value: data.deviceTypePname || data.deviceTypePName || LABELS.NO_GATHER_DEVICE,
      },
      {
        label: '设备类型',
        value: data.deviceTypeName || data.faultDevice || LABELS.NO_GATHER_DEVICE,
      },
      { label: '设备位置', value: address },
      {
        label: '九小场所/家庭名称',
        value: data._isNine ? data.unitName : '',
      },
      { label: '设备品牌', value: data.produceInfo.brand || '--' },
      { label: '规格型号', value: data.produceInfo.model || '--' },
      {
        label: '安装日期',
        value: data.installInfo.installDate || data.installInfo.install_date || '',
      },
    ]

    return temp.concat(base).filter(function (item) {
      return !!item.value
    })
  },
  normalizeAddress: function (item) {
    let address =
      (item.buildingName || '') +
      '' +
      (item.floorName || '') +
      '' +
      (item.deviceAddress || item.faultAddress || '')
    if (item._isNine) {
      address = (item.houseNumber || '') + (item.deviceAddress || '')
    }
    return address === '' ? LABELS.NO_GATHER : address
  },
  normalizeLoop: function (item) {
    const laMake = item.laMake || ''
    const laLoop = item.laLoop || ''
    const laPoint = item.laPoint || ''
    const loop = laMake + (laLoop ? '-' + laLoop : '') + (laPoint ? '-' + laPoint : '')

    // var loop = (item.laMake || '') + '-' + (item.laLoop || '') + '-' + (item.laPoint || '');
    if (loop === '') {
      return +item.deviceClassification === 1 ? LABELS.NO_GATHER : LABELS.ZERO
    }
    return loop
  },
}

export function loopAddAllToTree(tree: any[]) {
  tree.unshift({
    label: '全部',
    value: '',
  })
  tree.forEach((element) => {
    if (element.children?.length) {
      loopAddAllToTree(element.children)
    }
  })
}

function loopUnitTreeToLabelValue(tree: any[]) {
  tree.unshift({
    label: '全部',
    value: '',
  })
  tree.forEach((element) => {
    element.children = element.floors || element.builds || []
    if (element.children.length) {
      loopUnitTreeToLabelValue(element.children)
    }
    if (element.value === undefined) {
      element.value = element.floorId || element.buildId
    }
    if (element.label === undefined) {
      element.label = element.floorName || element.buildName
    }
  })
}
export function getBuildingAndFloorTree(unitId: string) {
  return http<any>({
    method: 'POST',
    url: '/app/monitor/getTreeBuildingInfo',
    query: {
      unitId,
      orgCode: userInfo.orgCode,
    },
  }).then((res) => {
    try {
      const data = JSON.parse(res.data)
      const builds = data.builds
      loopUnitTreeToLabelValue(builds)
      return builds
    } catch (error) {
      return []
    }
  })
}
