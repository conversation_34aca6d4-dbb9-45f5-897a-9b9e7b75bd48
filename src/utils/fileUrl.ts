/**
 * 获取完整的文件URL
 * @description 将相对路径转换为完整的文件URL，如果已经是完整URL则直接返回
 * @param {string} url - 文件路径，可以是相对路径或完整的http/https URL
 * @returns {string} 完整的文件URL
 * @example
 * // 相对路径
 * getFullFileUrl("20250815/image.jpg")
 * // 返回: VITE_HOST + "20250815/image.jpg"
 *
 * // 完整URL
 * getFullFileUrl("https://example.com/image.jpg")
 * // 返回: "https://example.com/image.jpg"
 */
const VITE_HOST = import.meta.env.VITE_HOST
export function getFullFileUrl(url: string) {
  if (!url) return ''

  // 如果已经是完整的 URL（以 http 开头），直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  if (url.startsWith('/ehs')) {
    return VITE_HOST + url
  } else {
    return VITE_HOST + '/ehs' + url
  }
}

/**
 * 获取完整的缩略图URL
 * @description 将原始图片路径转换为缩略图URL，支持自定义尺寸参数
 * @param {string} url - 原始图片路径，可以是相对路径或完整的http/https URL
 * @param {string} [size='80x80'] - 缩略图尺寸，格式为 "宽x高"，如 "80x80", "120x120"
 * @returns {string} 完整的缩略图URL
 * @example
 * // 相对路径，默认尺寸
 * getFullThumbnailUrl("20250815/image.jpg")
 * // 返回: VITE_HOST + "20250815/image_80x80.jpg"
 *
 * // 相对路径，自定义尺寸
 * getFullThumbnailUrl("20250815/image.jpg", "120x120")
 * // 返回: VITE_HOST + "20250815/image_120x120.jpg"
 *
 * // 完整URL（不支持缩略图转换）
 * getFullThumbnailUrl("https://example.com/image.jpg", "80x80")
 * // 返回: "https://example.com/image.jpg"
 */
export function getFullThumbnailUrl(url: string, size = '80x80') {
  if (!url) return ''

  // 如果已经是完整的 URL（以 http 开头），直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  const lastDotIndex = url.lastIndexOf('.')
  if (lastDotIndex === -1) return VITE_HOST + url

  const baseName = url.substring(0, lastDotIndex)
  const extension = url.substring(lastDotIndex)

  return VITE_HOST + baseName + extension
}
