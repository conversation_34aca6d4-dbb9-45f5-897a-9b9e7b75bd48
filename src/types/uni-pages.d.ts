/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/accidentDetail/accidentDetail" |
       "/pages/deviceLocation/index" |
       "/pages/error/error" |
       "/pages/inspectPath/index" |
       "/pages/inspectionDetail/alarmListItem" |
       "/pages/inspectionDetail/index" |
       "/pages/inspectionDetail/misinformation" |
       "/pages/ponitDetail/index" |
       "/pages/taskDetailLive/index" |
       "/pages/inspectionDetail/compoments/upload-button";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/" | "/"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
