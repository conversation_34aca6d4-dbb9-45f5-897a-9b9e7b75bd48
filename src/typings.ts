// 全局要用的类型放到这里

type IResData<T> = {
  code: number
  msg: string
  data: T
}

// uni.uploadFile文件上传参数
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}

type IUserInfo = {
  nickname?: string
  avatar?: string
  /** 微信的 openid，非微信没有这个字段 */
  openid?: string
  token?: string
  zhId?: string
  createdBy?: string
  orgCode?: string
}
interface Window {
  $SYS_CFG: any
  $_ScriptInjector: any
  $receiveData: (data: any) => void
  webUni: any
  IndoorMap: any
  CONST_GSOptions: any
  IndoorThree: any
  newIndoorService: any
  CONST_Function_DeviceStateValueConvertFun_Default_3: any
  THREE: any
  GISShare: any
  CONST_GSCache: any
  CONST_GSParams: any
  DicCache: any
  CONST_StyleInfo_Default_Deep: any
  CONST_StyleInfo_Default_Deep2: any
}
